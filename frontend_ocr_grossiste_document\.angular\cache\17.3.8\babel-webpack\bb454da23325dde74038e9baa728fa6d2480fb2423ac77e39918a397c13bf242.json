{"ast": null, "code": "var _AuthInterceptor;\nimport { throwError } from 'rxjs';\nimport { jwtDecode } from 'jwt-decode';\nimport { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/api.service\";\nexport class AuthInterceptor {\n  constructor(apiService) {\n    this.apiService = apiService;\n  }\n  intercept(req, next) {\n    const tokenUser = localStorage.getItem('tokenUser');\n    const tokenTenant = localStorage.getItem('tokenTenant');\n    const tokenLocal = localStorage.getItem('token');\n    const srcApp = localStorage.getItem('src_app');\n    // Check if we have the required tokens based on platform\n    const hasRequiredTokens = srcApp === 'pharmalier' ? tokenUser && tokenLocal // Pharmalier only needs user token and local token\n    : tokenUser && tokenTenant && tokenLocal; // WinPlusPharma needs all three tokens\n    if (hasRequiredTokens) {\n      if (!this.apiService.isLoggedIn()) {\n        this.logout();\n        return throwError(() => new Error('Token has expired'));\n      }\n      let headers = req.headers.set('Authorization', `Bearer ${tokenLocal}`);\n      // Add platform-specific headers\n      if (srcApp === 'pharmalier') {\n        // Pharmalier only needs user authorization\n        headers = headers.set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`);\n      } else {\n        // WinPlusPharma needs both tenant and user authorization\n        headers = headers.set('AuthorizationTenant', `BearerTenant ${JSON.parse(tokenTenant).accessToken}`).set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`);\n      }\n      const cloned = req.clone({\n        headers\n      });\n      return next.handle(cloned).pipe(catchError(error => {\n        if (error.status === 401 || error.status === 403) {\n          this.logout();\n        }\n        return throwError(() => error);\n      }));\n    } else {\n      return next.handle(req);\n    }\n  }\n  isTokenExpired(token) {\n    const decodedToken = jwtDecode(token);\n    const expirationDate = new Date(decodedToken.exp * 1000);\n    return expirationDate < new Date();\n  }\n  logout() {\n    localStorage.removeItem('tokenUser');\n    localStorage.removeItem('tokenTenant');\n    localStorage.removeItem('token');\n    localStorage.removeItem('credentials');\n    // Keep src_app to remember platform choice\n    // Redirect the user to the login page or show a logout message\n  }\n}\n_AuthInterceptor = AuthInterceptor;\n_AuthInterceptor.ɵfac = function AuthInterceptor_Factory(t) {\n  return new (t || _AuthInterceptor)(i0.ɵɵinject(i1.ApiService));\n};\n_AuthInterceptor.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _AuthInterceptor,\n  factory: _AuthInterceptor.ɵfac\n});", "map": {"version": 3, "names": ["throwError", "jwtDecode", "catchError", "AuthInterceptor", "constructor", "apiService", "intercept", "req", "next", "tokenUser", "localStorage", "getItem", "tokenTenant", "tokenLocal", "srcApp", "hasRequiredTokens", "isLoggedIn", "logout", "Error", "headers", "set", "JSON", "parse", "accessToken", "cloned", "clone", "handle", "pipe", "error", "status", "isTokenExpired", "token", "decodedToken", "expirationDate", "Date", "exp", "removeItem", "i0", "ɵɵinject", "i1", "ApiService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpEvent, HttpInterceptor, <PERSON>ttpH<PERSON><PERSON>, HttpRequest } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { ApiService } from '../services/api.service';\r\n\r\n\r\ninterface DecodedToken {\r\n  exp: number;\r\n  iat: number;\r\n  sub: string;  // typically the user identifier\r\n  // Add other expected fields here\r\n}\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n\r\n  constructor(private apiService: ApiService) { }\r\n\r\n  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n    const tokenLocal = localStorage.getItem('token');\r\n    const srcApp = localStorage.getItem('src_app');\r\n\r\n    // Check if we have the required tokens based on platform\r\n    const hasRequiredTokens = srcApp === 'pharmalier'\r\n      ? (tokenUser && tokenLocal) // Pharmalier only needs user token and local token\r\n      : (tokenUser && tokenTenant && tokenLocal); // WinPlusPharma needs all three tokens\r\n\r\n    if (hasRequiredTokens) {\r\n      if (!this.apiService.isLoggedIn()) {\r\n        this.logout();\r\n        return throwError(() => new Error('Token has expired'));\r\n      }\r\n\r\n      let headers = req.headers.set('Authorization', `Bearer ${tokenLocal}`);\r\n\r\n      // Add platform-specific headers\r\n      if (srcApp === 'pharmalier') {\r\n        // Pharmalier only needs user authorization\r\n        headers = headers.set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser!).accessToken}`);\r\n      } else {\r\n        // WinPlusPharma needs both tenant and user authorization\r\n        headers = headers\r\n          .set('AuthorizationTenant', `BearerTenant ${JSON.parse(tokenTenant!).accessToken}`)\r\n          .set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser!).accessToken}`);\r\n      }\r\n\r\n      const cloned = req.clone({ headers });\r\n      return next.handle(cloned).pipe(\r\n        catchError((error) => {\r\n          if (error.status === 401 || error.status === 403) {\r\n            this.logout();\r\n          }\r\n          return throwError(() => error);\r\n        })\r\n      );\r\n    } else {\r\n      return next.handle(req);\r\n    }\r\n  }\r\n\r\n  isTokenExpired(token: string): boolean {\r\n    const decodedToken: any = jwtDecode(token);\r\n    const expirationDate = new Date(decodedToken.exp * 1000);\r\n    return expirationDate < new Date();\r\n  }\r\n\r\n\r\n\r\n  logout() {\r\n    localStorage.removeItem('tokenUser');\r\n    localStorage.removeItem('tokenTenant');\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('credentials');\r\n    // Keep src_app to remember platform choice\r\n    // Redirect the user to the login page or show a logout message\r\n  }\r\n}"], "mappings": ";AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,UAAU,QAAQ,gBAAgB;;;AAY3C,OAAM,MAAOC,eAAe;EAE1BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAgB;EAE9CC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMG,MAAM,GAAGJ,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAE9C;IACA,MAAMI,iBAAiB,GAAGD,MAAM,KAAK,YAAY,GAC5CL,SAAS,IAAII,UAAU,CAAE;IAAA,EACzBJ,SAAS,IAAIG,WAAW,IAAIC,UAAW,CAAC,CAAC;IAE9C,IAAIE,iBAAiB,EAAE;MACrB,IAAI,CAAC,IAAI,CAACV,UAAU,CAACW,UAAU,EAAE,EAAE;QACjC,IAAI,CAACC,MAAM,EAAE;QACb,OAAOjB,UAAU,CAAC,MAAM,IAAIkB,KAAK,CAAC,mBAAmB,CAAC,CAAC;;MAGzD,IAAIC,OAAO,GAAGZ,GAAG,CAACY,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUP,UAAU,EAAE,CAAC;MAEtE;MACA,IAAIC,MAAM,KAAK,YAAY,EAAE;QAC3B;QACAK,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,cAAcC,IAAI,CAACC,KAAK,CAACb,SAAU,CAAC,CAACc,WAAW,EAAE,CAAC;OAC/F,MAAM;QACL;QACAJ,OAAO,GAAGA,OAAO,CACdC,GAAG,CAAC,qBAAqB,EAAE,gBAAgBC,IAAI,CAACC,KAAK,CAACV,WAAY,CAAC,CAACW,WAAW,EAAE,CAAC,CAClFH,GAAG,CAAC,mBAAmB,EAAE,cAAcC,IAAI,CAACC,KAAK,CAACb,SAAU,CAAC,CAACc,WAAW,EAAE,CAAC;;MAGjF,MAAMC,MAAM,GAAGjB,GAAG,CAACkB,KAAK,CAAC;QAAEN;MAAO,CAAE,CAAC;MACrC,OAAOX,IAAI,CAACkB,MAAM,CAACF,MAAM,CAAC,CAACG,IAAI,CAC7BzB,UAAU,CAAE0B,KAAK,IAAI;QACnB,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,IAAID,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;UAChD,IAAI,CAACZ,MAAM,EAAE;;QAEf,OAAOjB,UAAU,CAAC,MAAM4B,KAAK,CAAC;MAChC,CAAC,CAAC,CACH;KACF,MAAM;MACL,OAAOpB,IAAI,CAACkB,MAAM,CAACnB,GAAG,CAAC;;EAE3B;EAEAuB,cAAcA,CAACC,KAAa;IAC1B,MAAMC,YAAY,GAAQ/B,SAAS,CAAC8B,KAAK,CAAC;IAC1C,MAAME,cAAc,GAAG,IAAIC,IAAI,CAACF,YAAY,CAACG,GAAG,GAAG,IAAI,CAAC;IACxD,OAAOF,cAAc,GAAG,IAAIC,IAAI,EAAE;EACpC;EAIAjB,MAAMA,CAAA;IACJP,YAAY,CAAC0B,UAAU,CAAC,WAAW,CAAC;IACpC1B,YAAY,CAAC0B,UAAU,CAAC,aAAa,CAAC;IACtC1B,YAAY,CAAC0B,UAAU,CAAC,OAAO,CAAC;IAChC1B,YAAY,CAAC0B,UAAU,CAAC,aAAa,CAAC;IACtC;IACA;EACF;;mBA/DWjC,eAAe;;mBAAfA,gBAAe,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAfrC,gBAAe;EAAAsC,OAAA,EAAftC,gBAAe,CAAAuC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}