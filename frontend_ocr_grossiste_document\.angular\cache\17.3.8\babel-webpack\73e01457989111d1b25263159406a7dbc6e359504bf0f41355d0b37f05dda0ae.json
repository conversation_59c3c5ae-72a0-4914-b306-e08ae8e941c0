{"ast": null, "code": "var _WelcomePage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class WelcomePage {\n  constructor(navCtrl) {\n    this.navCtrl = navCtrl;\n  }\n  ngOnInit() {\n    console.log(\"Welcome Page\");\n  }\n  login_page() {\n    this.navCtrl.navigateRoot('/platform-selection');\n  }\n}\n_WelcomePage = WelcomePage;\n_WelcomePage.ɵfac = function WelcomePage_Factory(t) {\n  return new (t || _WelcomePage)(i0.ɵɵdirectiveInject(i1.NavController));\n};\n_WelcomePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _WelcomePage,\n  selectors: [[\"app-welcome\"]],\n  decls: 35,\n  vars: 0,\n  consts: [[1, \"welcome-wrapper\"], [\"size\", \"12\", 1, \"slide-content\"], [\"src\", \"/assets/icon-welcome.svg\", 1, \"slide-image\"], [1, \"content-slide\"], [1, \"platform-selection-row\"], [\"size\", \"12\"], [1, \"platform-selection-header\"], [1, \"platform-buttons\"], [1, \"platform-option\", 3, \"click\"], [1, \"platform-image-container\"], [\"src\", \"assets/onboarding_images/winpluspharm.svg\", \"alt\", \"WinPlus Pharma\", 1, \"platform-image\"], [1, \"platform-info\"], [\"src\", \"assets/onboarding_images/winpharm.svg\", \"alt\", \"Pharmalier\", 1, \"platform-image\"]],\n  template: function WelcomePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"ion-header\");\n      i0.ɵɵelementStart(1, \"ion-content\")(2, \"div\", 0)(3, \"ion-row\")(4, \"ion-col\", 1);\n      i0.ɵɵelement(5, \"img\", 2);\n      i0.ɵɵelementStart(6, \"div\", 3)(7, \"h2\");\n      i0.ɵɵtext(8, \"Scannez ,r\\u00E9cup\\u00E9rez, automatisez !\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"p\");\n      i0.ɵɵtext(10, \"Vos bons de livraison enregistr\\u00E9s automatiquement en un instant.\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(11, \"ion-row\", 4)(12, \"ion-col\", 5)(13, \"div\", 6)(14, \"h3\");\n      i0.ɵɵtext(15, \"Choisir votre plateforme\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"p\");\n      i0.ɵɵtext(17, \"S\\u00E9lectionnez la plateforme avec laquelle vous souhaitez vous connecter\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(18, \"div\", 7)(19, \"div\", 8);\n      i0.ɵɵlistener(\"click\", function WelcomePage_Template_div_click_19_listener() {\n        return ctx.selectPlatform(\"winpluspharma\");\n      });\n      i0.ɵɵelementStart(20, \"div\", 9);\n      i0.ɵɵelement(21, \"img\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"div\", 11)(23, \"h4\");\n      i0.ɵɵtext(24, \"WinPlus Pharma\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(25, \"p\");\n      i0.ɵɵtext(26, \"Connexion avec votre compte WinPlus\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(27, \"div\", 8);\n      i0.ɵɵlistener(\"click\", function WelcomePage_Template_div_click_27_listener() {\n        return ctx.selectPlatform(\"pharmalier\");\n      });\n      i0.ɵɵelementStart(28, \"div\", 9);\n      i0.ɵɵelement(29, \"img\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"div\", 11)(31, \"h4\");\n      i0.ɵɵtext(32, \"Pharmalier\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"p\");\n      i0.ɵɵtext(34, \"Connexion avec votre compte Pharmalier\");\n      i0.ɵɵelementEnd()()()()()()()();\n    }\n  },\n  dependencies: [i1.IonCol, i1.IonContent, i1.IonHeader, i1.IonRow],\n  styles: [\"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\", sans-serif;\\n  font-optical-sizing: auto;\\n}\\n\\nion-content[_ngcontent-%COMP%]::part(scroll) {\\n  overflow-y: auto;\\n}\\n\\nion-button[_ngcontent-%COMP%]::part(native) {\\n  --padding-top: 20px !important;\\n  --padding-bottom: 20px !important;\\n}\\n\\n.welcome-wrapper[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/bg-welcome.png\\\") no-repeat center center fixed;\\n  background-size: cover;\\n  height: 100%;\\n}\\n\\nion-footer[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  padding: 10px;\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: row;\\n  align-items: center;\\n}\\n\\nion-toolbar[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --ion-color-primary: #2f4fcd;\\n}\\n\\nion-button[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\nion-button.welcome-button[_ngcontent-%COMP%] {\\n  --background: #1f41bb;\\n  --background-activated: #1f41bb;\\n  --border-radius: 8px;\\n  width: 65%;\\n  text-align: center;\\n  box-shadow: 0px 16px 20px rgb(203, 214, 255); \\n\\n  color: #fff;\\n  font-size: 20px;\\n  font-weight: bold;\\n}\\n\\nion-row[_ngcontent-%COMP%] {\\n  height: 100%;\\n  height: 85vh;\\n}\\n\\n  ion-row ion-col {\\n  padding-bottom: 0 !important;\\n}\\n\\n  ion-col {\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: space-evenly;\\n  align-items: center;\\n}\\n\\n  img {\\n  width: auto;\\n  max-width: 100%;\\n  height: auto;\\n  max-height: 100%;\\n}\\n\\n  .content-slide {\\n  text-align: left;\\n  padding: 0 20px;\\n}\\n\\n  .content-slide h2 {\\n  font-family: \\\"Poppins\\\", \\\"Inter\\\", sans-serif !important;\\n  font-weight: bold;\\n  font-style: normal;\\n  font-size: 30px;\\n  color: #1f41bb;\\n}\\n\\n  .content-slide p {\\n  padding-right: 20px;\\n  margin-top: 40px;\\n  letter-spacing: 1.1px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["WelcomePage", "constructor", "navCtrl", "ngOnInit", "console", "log", "login_page", "navigateRoot", "i0", "ɵɵdirectiveInject", "i1", "NavController", "selectors", "decls", "vars", "consts", "template", "WelcomePage_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "WelcomePage_Template_div_click_19_listener", "selectPlatform", "WelcomePage_Template_div_click_27_listener"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\welcome\\welcome.page.ts", "C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\welcome\\welcome.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NavController } from '@ionic/angular';\r\n\r\n@Component({\r\n  selector: 'app-welcome',\r\n  templateUrl: './welcome.page.html',\r\n  styleUrls: ['./welcome.page.scss'],\r\n})\r\nexport class WelcomePage implements OnInit {\r\n\r\n  constructor(private navCtrl: NavController) {}\r\n\r\n  ngOnInit() {\r\n    console.log(\"Welcome Page\");\r\n    \r\n  }\r\n\r\n  login_page(){\r\n    this.navCtrl.navigateRoot('/platform-selection');\r\n  }\r\n}\r\n", "<ion-header>\r\n\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <div class=\"welcome-wrapper\">\r\n    <ion-row>\r\n      <ion-col size=\"12\" class=\"slide-content\">\r\n        <img src=\"/assets/icon-welcome.svg\" class=\"slide-image\" />\r\n        <div class=\"content-slide\">\r\n          <h2><PERSON><PERSON><PERSON> ,<PERSON><PERSON><PERSON><PERSON><PERSON>, automatisez !</h2>\r\n          <p>Vos bons de livraison enregistrés automatiquement en un instant.</p>\r\n        </div>\r\n      </ion-col>\r\n    </ion-row>\r\n\r\n    <!-- Platform Selection Buttons -->\r\n    <ion-row class=\"platform-selection-row\">\r\n      <ion-col size=\"12\">\r\n        <div class=\"platform-selection-header\">\r\n          <h3>Choisir votre plateforme</h3>\r\n          <p>Sélectionnez la plateforme avec laquelle vous souhaitez vous connecter</p>\r\n        </div>\r\n\r\n        <div class=\"platform-buttons\">\r\n          <div class=\"platform-option\" (click)=\"selectPlatform('winpluspharma')\">\r\n            <div class=\"platform-image-container\">\r\n              <img src=\"assets/onboarding_images/winpluspharm.svg\" alt=\"WinPlus Pharma\" class=\"platform-image\">\r\n            </div>\r\n            <div class=\"platform-info\">\r\n              <h4>WinPlus Pharma</h4>\r\n              <p>Connexion avec votre compte WinPlus</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"platform-option\" (click)=\"selectPlatform('pharmalier')\">\r\n            <div class=\"platform-image-container\">\r\n              <img src=\"assets/onboarding_images/winpharm.svg\" alt=\"Pharmalier\" class=\"platform-image\">\r\n            </div>\r\n            <div class=\"platform-info\">\r\n              <h4>Pharmalier</h4>\r\n              <p>Connexion avec votre compte Pharmalier</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ion-col>\r\n    </ion-row>\r\n  </div>\r\n</ion-content>"], "mappings": ";;;AAQA,OAAM,MAAOA,WAAW;EAEtBC,YAAoBC,OAAsB;IAAtB,KAAAA,OAAO,GAAPA,OAAO;EAAkB;EAE7CC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAE7B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACJ,OAAO,CAACK,YAAY,CAAC,qBAAqB,CAAC;EAClD;;eAXWP,WAAW;;mBAAXA,YAAW,EAAAQ,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;AAAA;;QAAXX,YAAW;EAAAY,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCRxBV,EAAA,CAAAY,SAAA,iBAEa;MAKPZ,EAHN,CAAAa,cAAA,kBAAa,aACkB,cAClB,iBACkC;MACvCb,EAAA,CAAAY,SAAA,aAA0D;MAExDZ,EADF,CAAAa,cAAA,aAA2B,SACrB;MAAAb,EAAA,CAAAc,MAAA,kDAAiC;MAAAd,EAAA,CAAAe,YAAA,EAAK;MAC1Cf,EAAA,CAAAa,cAAA,QAAG;MAAAb,EAAA,CAAAc,MAAA,6EAAgE;MAGzEd,EAHyE,CAAAe,YAAA,EAAI,EACnE,EACE,EACF;MAMJf,EAHN,CAAAa,cAAA,kBAAwC,kBACnB,cACsB,UACjC;MAAAb,EAAA,CAAAc,MAAA,gCAAwB;MAAAd,EAAA,CAAAe,YAAA,EAAK;MACjCf,EAAA,CAAAa,cAAA,SAAG;MAAAb,EAAA,CAAAc,MAAA,mFAAsE;MAC3Ed,EAD2E,CAAAe,YAAA,EAAI,EACzE;MAGJf,EADF,CAAAa,cAAA,cAA8B,cAC2C;MAA1Cb,EAAA,CAAAgB,UAAA,mBAAAC,2CAAA;QAAA,OAASN,GAAA,CAAAO,cAAA,CAAe,eAAe,CAAC;MAAA,EAAC;MACpElB,EAAA,CAAAa,cAAA,cAAsC;MACpCb,EAAA,CAAAY,SAAA,eAAiG;MACnGZ,EAAA,CAAAe,YAAA,EAAM;MAEJf,EADF,CAAAa,cAAA,eAA2B,UACrB;MAAAb,EAAA,CAAAc,MAAA,sBAAc;MAAAd,EAAA,CAAAe,YAAA,EAAK;MACvBf,EAAA,CAAAa,cAAA,SAAG;MAAAb,EAAA,CAAAc,MAAA,2CAAmC;MAE1Cd,EAF0C,CAAAe,YAAA,EAAI,EACtC,EACF;MAENf,EAAA,CAAAa,cAAA,cAAoE;MAAvCb,EAAA,CAAAgB,UAAA,mBAAAG,2CAAA;QAAA,OAASR,GAAA,CAAAO,cAAA,CAAe,YAAY,CAAC;MAAA,EAAC;MACjElB,EAAA,CAAAa,cAAA,cAAsC;MACpCb,EAAA,CAAAY,SAAA,eAAyF;MAC3FZ,EAAA,CAAAe,YAAA,EAAM;MAEJf,EADF,CAAAa,cAAA,eAA2B,UACrB;MAAAb,EAAA,CAAAc,MAAA,kBAAU;MAAAd,EAAA,CAAAe,YAAA,EAAK;MACnBf,EAAA,CAAAa,cAAA,SAAG;MAAAb,EAAA,CAAAc,MAAA,8CAAsC;MAOvDd,EAPuD,CAAAe,YAAA,EAAI,EACzC,EACF,EACF,EACE,EACF,EACN,EACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}