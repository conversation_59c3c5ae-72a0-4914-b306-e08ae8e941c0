@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content::part(scroll) {
  // overflow-y: hidden !important;
  // --overflow: hidden !important;

  overflow-y: auto;
}

ion-button::part(native) {
  --padding-top : 20px !important;
  --padding-bottom : 20px !important;
}
.welcome-wrapper {
  background: url("/assets/bg-welcome.png") no-repeat center center fixed;
  background-size: cover;
  height: 100%;
}

ion-footer {
  position: relative;
  // bottom: -30px;
  width: 100%;
  padding: 10px;
  display: flex;
  justify-content: center;
  flex-direction: row;
  align-items: center;
}

ion-toolbar {
  --background: transparent;
  --ion-color-primary: #2f4fcd;
}

ion-button {
  margin: 10px;
}

ion-button.welcome-button {
  --background: #1f41bb;
  --background-activated: #1f41bb;
  --border-radius: 8px;
  width: 65%;
  text-align: center;
  box-shadow: 0px 16px 20px rgba(203, 214, 255, 1); /* Add shadow */
  color: #fff;
  font-size: 20px;
  font-weight: bold;
}

ion-row {
  height: 100%;
  height: 85vh;
}

::ng-deep ion-row ion-col {
  //   padding-top: 0 !important;
  padding-bottom: 0 !important;
}

::ng-deep ion-col {
  display: flex !important;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}

::ng-deep img {
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 100%;
}

::ng-deep .content-slide {
  text-align: left;
  padding: 0 20px;
}

::ng-deep .content-slide h2 {
  font-family: "Poppins", "Inter", sans-serif !important;
  font-weight: bold;
  font-style: normal;
  font-size: 30px;
  color: #1f41bb;
}

::ng-deep .content-slide p {
  padding-right: 20px;
  margin-top: 40px;
  letter-spacing: 1.1px;
}

// Platform Selection Styles
.platform-selection-row {
  height: auto !important;
  margin-top: 20px;
}

.platform-selection-header {
  text-align: center;
  margin-bottom: 20px;

  h3 {
    font-family: "Poppins", "Inter", sans-serif !important;
    font-weight: 600;
    font-size: 24px;
    color: #1f41bb;
    margin-bottom: 8px;
  }

  p {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.platform-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 0 20px;
}

.platform-option {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0px 8px 20px rgba(31, 65, 187, 0.15);
    border-color: #1f41bb;
  }

  &:active {
    transform: translateY(0);
  }
}

.platform-image-container {
  flex-shrink: 0;
  margin-right: 15px;

  .platform-image {
    width: 50px;
    height: 50px;
    object-fit: contain;
  }
}

.platform-info {
  flex: 1;

  h4 {
    font-family: "Poppins", "Inter", sans-serif !important;
    font-weight: 600;
    font-size: 18px;
    color: #1f41bb;
    margin: 0 0 5px 0;
  }

  p {
    font-size: 14px;
    color: #666;
    margin: 0;
    line-height: 1.4;
  }
}
