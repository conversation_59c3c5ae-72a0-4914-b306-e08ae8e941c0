import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';

@Component({
  selector: 'app-welcome',
  templateUrl: './welcome.page.html',
  styleUrls: ['./welcome.page.scss'],
})
export class WelcomePage implements OnInit {

  constructor(private navCtrl: NavController) {}

  ngOnInit() {
    console.log("Welcome Page");
  }

  selectPlatform(platform: 'winpluspharma' | 'pharmalier') {
    // Store the selected platform
    localStorage.setItem('src_app', platform);

    // Navigate to login with platform parameter
    this.navCtrl.navigateForward('/login', {
      queryParams: { platform: platform }
    });
  }
}
