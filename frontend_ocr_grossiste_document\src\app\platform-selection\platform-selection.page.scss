.platform-selection-wrapper {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.platform-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 600px;
  width: 100%;
}

.content-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
  }
}

.platform-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  margin-bottom: 30px;
}

.platform-option {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
  }

  &:active {
    transform: translateY(0);
  }
}

.platform-image-container {
  flex-shrink: 0;
  margin-right: 20px;
  
  .platform-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
  }
}

.platform-info {
  flex: 1;

  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
  }

  p {
    margin: 0 0 4px 0;
    color: #666;
    font-size: 1rem;
  }

  .platform-description {
    font-size: 0.9rem;
    color: #999;
    font-style: italic;
  }
}

.switch-platform-link {
  text-align: center;
  margin-top: 20px;

  ion-button {
    --color: white;
    font-size: 1rem;
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .platform-option {
    flex-direction: column;
    text-align: center;
    padding: 20px;

    .platform-image-container {
      margin-right: 0;
      margin-bottom: 16px;
    }
  }

  .content-header h1 {
    font-size: 2rem;
  }

  .platform-buttons {
    gap: 16px;
  }
}

// Very small screens
@media (max-width: 480px) {
  .platform-selection-wrapper {
    padding: 16px;
  }

  .content-header h1 {
    font-size: 1.8rem;
  }

  .platform-option {
    padding: 16px;
  }

  .platform-image-container .platform-image {
    width: 60px;
    height: 60px;
  }
}
