{"ast": null, "code": "var _PlatformSelectionPageRoutingModule;\nimport { RouterModule } from '@angular/router';\nimport { PlatformSelectionPage } from './platform-selection.page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: PlatformSelectionPage\n}];\nexport class PlatformSelectionPageRoutingModule {}\n_PlatformSelectionPageRoutingModule = PlatformSelectionPageRoutingModule;\n_PlatformSelectionPageRoutingModule.ɵfac = function PlatformSelectionPageRoutingModule_Factory(t) {\n  return new (t || _PlatformSelectionPageRoutingModule)();\n};\n_PlatformSelectionPageRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _PlatformSelectionPageRoutingModule\n});\n_PlatformSelectionPageRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [RouterModule.forChild(routes), RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PlatformSelectionPageRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "PlatformSelectionPage", "routes", "path", "component", "PlatformSelectionPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\platform-selection\\platform-selection-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { Routes, RouterModule } from '@angular/router';\n\nimport { PlatformSelectionPage } from './platform-selection.page';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: PlatformSelectionPage\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class PlatformSelectionPageRoutingModule {}\n"], "mappings": ";AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AAEtD,SAASC,qBAAqB,QAAQ,2BAA2B;;;AAEjE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,kCAAkC;sCAAlCA,kCAAkC;;mBAAlCA,mCAAkC;AAAA;;QAAlCA;AAAkC;;YAHnCL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;AAAA;;2EAEXK,kCAAkC;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFnCT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}