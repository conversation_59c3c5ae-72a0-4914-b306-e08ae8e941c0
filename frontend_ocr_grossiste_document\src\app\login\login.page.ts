import { Compo<PERSON>, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { NavController, ToastController, LoadingController, Platform  } from '@ionic/angular';
import { ApiService } from '../services/api.service';
import { LoginRequest, LoginResponse, TenantLoginRequest, TenantLoginResponse, PharmalienLoginRequest, PharmalienLoginResponse } from '../../models/login';
import { environment } from '../../environments/environment';
import { Keyboard } from '@capacitor/keyboard';
import { retry, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { StorageService } from '../services/storage.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit, OnDestroy {
  tenantUsername: string = '';
  tenantPassword: string = '';
  username: string = '';
  password: string = '';
  showTenantLogin: boolean = true;
  tenantToken: string = '';
  hasSeenOnboarding: boolean = false;
  localCredentials: any = {};

  // Platform selection
  currentPlatform: 'winpluspharma' | 'pharmalier' = 'winpluspharma';
  isPharmalierLogin: boolean = false;

  constructor(
    private navCtrl: NavController,
    private apiService: ApiService,
    private toastController: ToastController,
    private loadingController: LoadingController,
    private storageService: StorageService,
    private route: ActivatedRoute,
    private platform: Platform
  ) {}

  async ngOnInit() {
    // Check platform from route parameters or localStorage
    this.route.queryParams.subscribe(params => {
      if (params['platform']) {
        this.currentPlatform = params['platform'];
        localStorage.setItem('src_app', this.currentPlatform);
      } else {
        // Check localStorage for remembered platform
        const savedPlatform = localStorage.getItem('src_app');
        if (savedPlatform) {
          this.currentPlatform = savedPlatform as 'winpluspharma' | 'pharmalier';
        } else {
          // No platform selected, redirect to platform selection
          this.navCtrl.navigateRoot('/platform-selection');
          return;
        }
      }

      this.isPharmalierLogin = this.currentPlatform === 'pharmalier';

      // Set up form based on platform
      if (this.isPharmalierLogin) {
        this.showTenantLogin = false; // Pharmalier doesn't need tenant login
      } else {
        this.showTenantLogin = true; // WinPlusPharma needs tenant login
      }
    });

    if (environment.production) {
      this.tenantUsername = '';
      this.tenantPassword = '';
      this.username = '';
      this.password = '';
    } else {
      this.tenantUsername = '0001';
      this.tenantPassword = '123456';
      this.username = 'PH';
      this.password = 'PH';
    }

    // Load the credentials from the local storage
    this.localCredentials = await JSON.parse(localStorage.getItem('credentials') as any);
    if (this.localCredentials) {
      this.tenantUsername = this.localCredentials.tenantUsername;
      this.tenantPassword = this.localCredentials.tenantPassword
      this.username = this.localCredentials.username;
      this.password = this.localCredentials.password;
    }

    // Only add keyboard listeners on mobile platforms
    if (this.platform.is('capacitor')) {
      Keyboard.addListener('keyboardWillShow', () => {
        const footer = document.querySelector('ion-footer');
        footer?.classList.add('keyboard-open');
      });

      Keyboard.addListener('keyboardWillHide', () => {
        const footer = document.querySelector('ion-footer');
        footer?.classList.remove('keyboard-open');
      });
    }

    // Initialize storage
    await this.storageService.init();
  
    // Check if the user has seen onboarding
    this.hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');
  }


  ngOnDestroy() {
    // Only remove keyboard listeners on mobile platforms
    if (this.platform.is('capacitor')) {
      Keyboard.removeAllListeners();
    }
  }

  async tenantLogin() {

    // Check if email and password are empty
    if (this.tenantUsername === '' || this.tenantPassword === '') {
      const toast = this.toastController.create({
        message: 'Veuillez remplir les champs de connexion.',
        duration: 2000,
        color: 'danger',
      }).then(toast => toast.present());
      return;
    }

    const loading = await this.loadingController.create({
      message: 'Connexion de la pharmacie...',
    });
    await loading.present();
  
    const tenantRequest: TenantLoginRequest = {
      username: this.tenantUsername,
      password: this.tenantPassword,
    };
  
    this.apiService.tenantLogin(tenantRequest)
      .pipe(
        retry(1), // Retry the request once if it fails
        catchError(error => {
          console.log('Tenant login error:', error);
          
          if(error.status === 403){
              const toast =  this.toastController.create({
                message: error.error.detail,
                duration: 2000,
                color: 'danger',
              }).then(toast => toast.present());
          }
          else{
            const toast = this.toastController.create({
              message: 'La connexion de la pharmacie a échoué. Veuillez réessayer.',
              duration: 2000,
              color: 'danger',
            }).then(toast => toast.present());
          }
          console.error('Tenant login error:', error);
          return of(null); // Return an observable with null to continue the stream
        })
      )
      .subscribe(
        async (response: TenantLoginResponse | null) => {
          await loading.dismiss();
          if (response) {
            console.log('Tenant login response:', response);
            this.tenantToken = response.accessToken;
            this.showTenantLogin = false;
            const toast = await this.toastController.create({
              message: 'Pharmacie connectée avec succès !',
              duration: 2000,
              color: 'success',
            });
            toast.present();
          }
        }
      );
  }

  async userLogin() {
    if (this.isPharmalierLogin) {
      await this.pharmalierLogin();
    } else {
      await this.winplusUserLogin();
    }
  }

  async pharmalierLogin() {
    // Check if username and password are empty
    if (this.username === '' || this.password === '') {
      const toast = this.toastController.create({
        message: 'Veuillez remplir les champs de connexion.',
        duration: 2000,
        color: 'danger',
      }).then(toast => toast.present());
      return;
    }

    const loading = await this.loadingController.create({
      message: 'Connexion Pharmalier ...',
    });
    await loading.present();

    const pharmalierRequest: PharmalienLoginRequest = {
      username: this.username,
      password: this.password
    };

    this.apiService.pharmalienLogin(pharmalierRequest).subscribe(
      async (response: PharmalienLoginResponse) => {
        console.log('Pharmalien login response:', response);

        // Store the user data in local storage (Pharmalien only needs user data and token)
        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? ""));
        localStorage.setItem('token', response.local_token ?? "");
        localStorage.setItem('ocrMode', 'STANDARD');

        // Store the credentials in local storage
        this.localCredentials = {
          username: this.username,
          password: this.password,
          platform: 'pharmalier'
        };
        localStorage.setItem('credentials', JSON.stringify(this.localCredentials));

        const toast = await this.toastController.create({
          message: 'Connexion Pharmalier réussie!',
          duration: 2000,
          color: 'success',
        });
        toast.present();
        await loading.dismiss();

        if (!this.hasSeenOnboarding) {
          await this.storageService.set('hasSeenOnboarding', true);
          this.navCtrl.navigateRoot('/guide');
        } else {
          this.navCtrl.navigateRoot('/scan-bl');
        }
      },
      async (error) => {
        console.error('Pharmalier login error:', error);

        let errorMessage = error.error.detail || 'Connexion Pharmalier échouée. Veuillez vérifier vos identifiants.';
        errorMessage = errorMessage.replace(/^Internal server error: \d+: /, '');

        const toast = await this.toastController.create({
          message: errorMessage,
          duration: 2000,
          color: 'danger',
        });
        toast.present();
        await loading.dismiss();
      }
    );
  }

  async winplusUserLogin() {

    // Check if email and password are empty
    if (this.username === '' || this.password === '') {
      const toast = this.toastController.create({
        message: 'Veuillez remplir les champs de connexion.',
        duration: 2000,
        color: 'danger',
      }).then(toast => toast.present());
      return;
    }

    const loading = await this.loadingController.create({
      message: 'Connexion WinPlus ...',
    });
    await loading.present();

    const userRequest: LoginRequest = {
      username: this.username,
      password: this.password,
      tenant_token: this.tenantToken  // Include tenant token in the request
    };

    this.apiService.userLogin(userRequest, this.tenantToken).subscribe(
      async (response: LoginResponse) => {
        console.log('User login response:', response);
        
        // Store the user data in local storage
        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? ""));
        localStorage.setItem('tokenTenant', JSON.stringify(response.tenant_data ?? ""));
        localStorage.setItem('token', response.local_token ?? "");
        localStorage.setItem('ocrMode', 'STANDARD');

        // Store the credentials in local storage
        this.localCredentials = {
          tenantUsername: this.tenantUsername,
          tenantPassword: this.tenantPassword,
          username: this.username,
          password: this.password,
          platform: 'winpluspharma'
        };
        localStorage.setItem('credentials', JSON.stringify(this.localCredentials));
        
        const toast = await this.toastController.create({
          message: 'Connexion WinPlus réussie!',
          duration: 2000,
          color: 'success',
        });
        toast.present();
        await loading.dismiss();

        if (!this.hasSeenOnboarding) {
          await this.storageService.set('hasSeenOnboarding', true);
          this.navCtrl.navigateRoot('/guide');
        }
        else{
          this.navCtrl.navigateRoot('/scan-bl');
        }
      },
      async (error) => {
        console.error('Login error:', error);
      
        // Extract the error message without "Internal server error: 400:"
        let errorMessage = error.error.detail || 'Connexion échouée. Veuillez vérifier vos identifiants.';
        
        // Remove "Internal server error: 400:" pattern from the message
        errorMessage = errorMessage.replace(/^Internal server error: \d+: /, '');

        const toast = await this.toastController.create({
          message: errorMessage,
          duration: 2000,
          color: 'danger',
        });
        toast.present();
        await loading.dismiss();
      }
    );
  }

  goBackToTenantLogin() {
    this.showTenantLogin = true;
    this.tenantToken = '';
  }

  switchPlatform() {
    this.navCtrl.navigateRoot('/platform-selection');
  }

  getPlatformDisplayName(): string {
    return this.isPharmalierLogin ? 'Pharmalier' : 'WinPlus Pharma';
  }
}