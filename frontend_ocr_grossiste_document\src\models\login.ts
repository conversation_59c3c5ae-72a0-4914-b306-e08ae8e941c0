// models/login.ts
export interface LoginRequest {
    username?: string;
    password?: string;
    tenant_token: string;
  }

  export interface TenantLoginRequest {
    username?: string;
    password?: string;
  }
  
  export interface TenantLoginResponse {
    accessToken: string;
    // Add any other fields returned by the tenant login API
  }
  
  export interface LoginResponse {
    tenant_data: any;  // This field will hold the entire JSON response from the tenant login
    user_data: any;  // This field will hold the entire JSON response from the user login
    local_token: string;  // This field will hold the local token
    message: string;
  }

  export interface PharmalienLoginRequest {
    username?: string;
    password?: string;
  }

  export interface PharmalienLoginResponse {
    user_data: any;  // This field will hold the entire JSON response from the pharmalien login
    local_token: string;  // This field will hold the local token
    message: string;
  }