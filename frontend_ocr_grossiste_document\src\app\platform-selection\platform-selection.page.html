<ion-header>
  <ion-toolbar>
    <ion-title>Choisir votre plateforme</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="platform-selection-wrapper">
    <ion-row>
      <ion-col size="12" class="platform-content">
        <div class="content-header">
          <h1>Choisir votre plateforme</h1>
          <p>Sélectionnez la plateforme avec laquelle vous souhaitez vous connecter</p>
        </div>

        <div class="platform-buttons">
          <div class="platform-option" (click)="selectPlatform('winpluspharma')">
            <div class="platform-image-container">
              <img src="assets/onboarding_images/winpluspharm.svg" alt="WinPlus Pharma" class="platform-image">
            </div>
            <div class="platform-info">
              <h2>WinPlus Pharma</h2>
              <p>Connexion avec votre compte WinPlus</p>
              <p class="platform-description">Authentification en deux étapes (Pharmacie + Utilisateur)</p>
            </div>
          </div>

          <div class="platform-option" (click)="selectPlatform('pharmalier')">
            <div class="platform-image-container">
              <img src="assets/onboarding_images/winpharm.svg" alt="Pharmalier" class="platform-image">
            </div>
            <div class="platform-info">
              <h2>Pharmalier</h2>
              <p>Connexion avec votre compte Pharmalier</p>
              <p class="platform-description">Authentification simple (Utilisateur uniquement)</p>
            </div>
          </div>
        </div>

        <div class="switch-platform-link" *ngIf="hasRememberedPlatform">
          <ion-button fill="clear" (click)="goToRememberedPlatform()">
            <ion-icon name="arrow-back-outline" slot="start"></ion-icon>
            Retour à {{ rememberedPlatformName }}
          </ion-button>
        </div>
      </ion-col>
    </ion-row>
  </div>
</ion-content>
