import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Determine if the script is executed from main.py or API
executed_file = Path(sys.argv[0]).name

if executed_file == '__main__.py':
    # Adjust paths for standalone script execution
    load_dotenv(Path(__file__).parents[2] / '.env.local')
    SYS_ARGV = 'main'
else:
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'local')
    env_file = Path(__file__).parents[2] / ('.env.prod' if ENVIRONMENT == 'prod' else '.env.local')
    load_dotenv(env_file)
    SYS_ARGV = 'api'

# Access the environment variables
DEBUG = os.getenv('DEBUG', 'False') == 'True'
TESSERACT_PATH = os.getenv('TESSERACT_PATH')
API_URL = os.getenv('API_URL')
TAP_URL = os.getenv('TAP_URL')

WINPLUS_AUTH_USER = os.getenv('WINPLUS_AUTH_USER')
WINPLUS_AUTH_TENANT = os.getenv('WINPLUS_AUTH_TENANT')
WINPLUS_URL = os.getenv('WINPLUS_URL')

# Pharmalien Authentication
PHARMALIEN_AUTH_URL = os.getenv('PHARMALIEN_AUTH_URL')

SECRET_KEY = os.getenv('SECRET_KEY')  # Change to a secure secret key
ALGORITHM = os.getenv('ALGORITHM')
ACCESS_TOKEN_EXPIRE_MINUTES = os.getenv('ACCESS_TOKEN_EXPIRE_MINUTES')

# PostgreSQL Database Configuration
POSTGRES_HOST = os.getenv('POSTGRES_HOST', 'localhost')
POSTGRES_PORT = os.getenv('POSTGRES_PORT', '5432')
POSTGRES_DB = os.getenv('POSTGRES_DB', 'ocr_document_grossiste')
POSTGRES_USER = os.getenv('POSTGRES_USER', 'postgres')
POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', 'postgres')
POSTGRES_SSL_MODE = os.getenv('POSTGRES_SSL_MODE', 'prefer')

# Database URL
DATABASE_URL = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}?sslmode={POSTGRES_SSL_MODE}"

# Ensure the TESSERACT_PATH is set
if not TESSERACT_PATH:
    raise ValueError("TESSERACT_PATH environment variable is not set.")
