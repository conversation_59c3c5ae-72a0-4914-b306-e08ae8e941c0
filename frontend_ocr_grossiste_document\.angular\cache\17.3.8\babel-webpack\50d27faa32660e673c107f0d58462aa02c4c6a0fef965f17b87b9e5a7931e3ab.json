{"ast": null, "code": "var _PlatformSelectionPageModule;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { PlatformSelectionPageRoutingModule } from './platform-selection-routing.module';\nimport { PlatformSelectionPage } from './platform-selection.page';\nimport * as i0 from \"@angular/core\";\nexport class PlatformSelectionPageModule {}\n_PlatformSelectionPageModule = PlatformSelectionPageModule;\n_PlatformSelectionPageModule.ɵfac = function PlatformSelectionPageModule_Factory(t) {\n  return new (t || _PlatformSelectionPageModule)();\n};\n_PlatformSelectionPageModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _PlatformSelectionPageModule\n});\n_PlatformSelectionPageModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, FormsModule, IonicModule, PlatformSelectionPageRoutingModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PlatformSelectionPageModule, {\n    declarations: [PlatformSelectionPage],\n    imports: [CommonModule, FormsModule, IonicModule, PlatformSelectionPageRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IonicModule", "PlatformSelectionPageRoutingModule", "PlatformSelectionPage", "PlatformSelectionPageModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\platform-selection\\platform-selection.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\n\nimport { IonicModule } from '@ionic/angular';\n\nimport { PlatformSelectionPageRoutingModule } from './platform-selection-routing.module';\n\nimport { PlatformSelectionPage } from './platform-selection.page';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    FormsModule,\n    IonicModule,\n    PlatformSelectionPageRoutingModule\n  ],\n  declarations: [PlatformSelectionPage]\n})\nexport class PlatformSelectionPageModule {}\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,kCAAkC,QAAQ,qCAAqC;AAExF,SAASC,qBAAqB,QAAQ,2BAA2B;;AAWjE,OAAM,MAAOC,2BAA2B;+BAA3BA,2BAA2B;;mBAA3BA,4BAA2B;AAAA;;QAA3BA;AAA2B;;YAPpCL,YAAY,EACZC,WAAW,EACXC,WAAW,EACXC,kCAAkC;AAAA;;2EAIzBE,2BAA2B;IAAAC,YAAA,GAFvBF,qBAAqB;IAAAG,OAAA,GALlCP,YAAY,EACZC,WAAW,EACXC,WAAW,EACXC,kCAAkC;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}