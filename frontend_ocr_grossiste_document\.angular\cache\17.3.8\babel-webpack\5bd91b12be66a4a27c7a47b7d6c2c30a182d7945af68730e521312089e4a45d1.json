{"ast": null, "code": "var _AuthGuard;\nimport { jwtDecode } from 'jwt-decode';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(router) {\n    this.router = router;\n  }\n  canActivate() {\n    const tokenUser = localStorage.getItem('tokenUser');\n    const tokenTenant = localStorage.getItem('tokenTenant');\n    const tokenLocal = localStorage.getItem('token');\n    const srcApp = localStorage.getItem('src_app');\n    // Check required tokens based on platform\n    const hasRequiredTokens = srcApp === 'pharmalier' ? tokenUser && tokenLocal // Pharmalier only needs user and local tokens\n    : tokenUser && tokenTenant && tokenLocal; // WinPlusPharma needs all three tokens\n    if (hasRequiredTokens) {\n      try {\n        // Check expiration for required tokens based on platform\n        const isTokenUserValid = this.checkTokenExpiration(tokenUser);\n        const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);\n        if (srcApp === 'pharmalier') {\n          // Pharmalier only needs user and local token validation\n          if (isTokenUserValid && isTokenLocalValid) {\n            return true;\n          }\n        } else {\n          // WinPlusPharma needs all three token validations\n          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);\n          if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {\n            return true;\n          }\n        }\n      } catch (error) {\n        console.error('Token validation error:', error);\n      }\n    }\n    // If any required token is missing or expired, redirect to welcome or login\n    if (!srcApp) {\n      this.router.navigate(['/welcome']);\n    } else {\n      this.router.navigate(['/login'], {\n        queryParams: {\n          platform: srcApp\n        }\n      });\n    }\n    return false;\n  }\n  checkTokenExpiration(token) {\n    const decodedToken = jwtDecode(token);\n    const expiration = moment((decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.exp) * 1000);\n    return moment(new Date()) < expiration;\n  }\n}\n_AuthGuard = AuthGuard;\n_AuthGuard.ɵfac = function AuthGuard_Factory(t) {\n  return new (t || _AuthGuard)(i0.ɵɵinject(i1.Router));\n};\n_AuthGuard.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _AuthGuard,\n  factory: _AuthGuard.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["jwtDecode", "moment", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "router", "canActivate", "tokenUser", "localStorage", "getItem", "tokenTenant", "tokenLocal", "srcApp", "hasRequiredTokens", "isTokenUserValid", "checkTokenExpiration", "isTokenLocalValid", "isTokenTenantValid", "error", "console", "navigate", "queryParams", "platform", "token", "decodedToken", "expiration", "exp", "Date", "i0", "ɵɵinject", "i1", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\interceptors\\auth.guard.ts"], "sourcesContent": ["// auth.guard.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { CanActivate, Router } from '@angular/router';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport * as moment from 'moment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthGuard implements CanActivate {\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  canActivate(): boolean {\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n    const tokenLocal = localStorage.getItem('token');\r\n    const srcApp = localStorage.getItem('src_app');\r\n\r\n    // Check required tokens based on platform\r\n    const hasRequiredTokens = srcApp === 'pharmalier'\r\n      ? (tokenUser && tokenLocal) // Pharmalier only needs user and local tokens\r\n      : (tokenUser && tokenTenant && tokenLocal); // WinPlusPharma needs all three tokens\r\n\r\n    if (hasRequiredTokens) {\r\n      try {\r\n        // Check expiration for required tokens based on platform\r\n        const isTokenUserValid = this.checkTokenExpiration(tokenUser!);\r\n        const isTokenLocalValid = this.checkTokenExpiration(tokenLocal!);\r\n\r\n        if (srcApp === 'pharmalier') {\r\n          // Pharmalier only needs user and local token validation\r\n          if (isTokenUserValid && isTokenLocalValid) {\r\n            return true;\r\n          }\r\n        } else {\r\n          // WinPlusPharma needs all three token validations\r\n          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant!);\r\n          if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {\r\n            return true;\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Token validation error:', error);\r\n      }\r\n    }\r\n\r\n    // If any required token is missing or expired, redirect to welcome or login\r\n    if (!srcApp) {\r\n      this.router.navigate(['/welcome']);\r\n    } else {\r\n      this.router.navigate(['/login'], { queryParams: { platform: srcApp } });\r\n    }\r\n    return false;\r\n  }\r\n\r\n  private checkTokenExpiration(token: string): boolean {\r\n    const decodedToken: any = jwtDecode(token);\r\n    const expiration = moment(decodedToken?.exp * 1000);\r\n    return moment(new Date()) < expiration;\r\n  }\r\n}"], "mappings": ";AAGA,SAASA,SAAS,QAAQ,YAAY;AACtC,OAAO,KAAKC,MAAM,MAAM,QAAQ;;;AAKhC,OAAM,MAAOC,SAAS;EAEpBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErCC,WAAWA,CAAA;IACT,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMG,MAAM,GAAGJ,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAE9C;IACA,MAAMI,iBAAiB,GAAGD,MAAM,KAAK,YAAY,GAC5CL,SAAS,IAAII,UAAU,CAAE;IAAA,EACzBJ,SAAS,IAAIG,WAAW,IAAIC,UAAW,CAAC,CAAC;IAE9C,IAAIE,iBAAiB,EAAE;MACrB,IAAI;QACF;QACA,MAAMC,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACR,SAAU,CAAC;QAC9D,MAAMS,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAACJ,UAAW,CAAC;QAEhE,IAAIC,MAAM,KAAK,YAAY,EAAE;UAC3B;UACA,IAAIE,gBAAgB,IAAIE,iBAAiB,EAAE;YACzC,OAAO,IAAI;;SAEd,MAAM;UACL;UACA,MAAMC,kBAAkB,GAAG,IAAI,CAACF,oBAAoB,CAACL,WAAY,CAAC;UAClE,IAAII,gBAAgB,IAAIG,kBAAkB,IAAID,iBAAiB,EAAE;YAC/D,OAAO,IAAI;;;OAGhB,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;;IAInD;IACA,IAAI,CAACN,MAAM,EAAE;MACX,IAAI,CAACP,MAAM,CAACe,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;KACnC,MAAM;MACL,IAAI,CAACf,MAAM,CAACe,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAEC,QAAQ,EAAEV;QAAM;MAAE,CAAE,CAAC;;IAEzE,OAAO,KAAK;EACd;EAEQG,oBAAoBA,CAACQ,KAAa;IACxC,MAAMC,YAAY,GAAQvB,SAAS,CAACsB,KAAK,CAAC;IAC1C,MAAME,UAAU,GAAGvB,MAAM,CAAC,CAAAsB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,GAAG,IAAG,IAAI,CAAC;IACnD,OAAOxB,MAAM,CAAC,IAAIyB,IAAI,EAAE,CAAC,GAAGF,UAAU;EACxC;;aAnDWtB,SAAS;;mBAATA,UAAS,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA;AAAA;;SAAT5B,UAAS;EAAA6B,OAAA,EAAT7B,UAAS,CAAA8B,IAAA;EAAAC,UAAA,EAFR;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}