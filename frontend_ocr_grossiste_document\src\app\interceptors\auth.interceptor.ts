import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, <PERSON>ttpH<PERSON><PERSON>, HttpRequest } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { jwtDecode } from 'jwt-decode';
import { catchError } from 'rxjs/operators';
import { ApiService } from '../services/api.service';


interface DecodedToken {
  exp: number;
  iat: number;
  sub: string;  // typically the user identifier
  // Add other expected fields here
}

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(private apiService: ApiService) { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const tokenLocal = localStorage.getItem('token');
    const srcApp = localStorage.getItem('src_app');

    // Check if we have the required tokens based on platform
    const hasRequiredTokens = srcApp === 'pharmalien'
      ? (tokenUser && tokenLocal) // Pharmalien only needs user token and local token
      : (tokenUser && tokenTenant && tokenLocal); // WinPlus needs all three tokens

    if (hasRequiredTokens) {
      if (!this.apiService.isLoggedIn()) {
        this.logout();
        return throwError(() => new Error('Token has expired'));
      }

      let headers = req.headers.set('Authorization', `Bearer ${tokenLocal}`);

      // Add platform-specific headers
      if (srcApp === 'pharmalien') {
        // Pharmalien only needs user authorization
        headers = headers.set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`);
      } else {
        // WinPlus needs both tenant and user authorization
        headers = headers
          .set('AuthorizationTenant', `BearerTenant ${JSON.parse(tokenTenant).accessToken}`)
          .set('AuthorizationUser', `BearerUser ${JSON.parse(tokenUser).accessToken}`);
      }

      const cloned = req.clone({ headers });
      return next.handle(cloned).pipe(
        catchError((error) => {
          if (error.status === 401 || error.status === 403) {
            this.logout();
          }
          return throwError(() => error);
        })
      );
    } else {
      return next.handle(req);
    }
  }

  isTokenExpired(token: string): boolean {
    const decodedToken: any = jwtDecode(token);
    const expirationDate = new Date(decodedToken.exp * 1000);
    return expirationDate < new Date();
  }



  logout() {
    localStorage.removeItem('tokenUser');
    localStorage.removeItem('tokenTenant');
    localStorage.removeItem('token');
    localStorage.removeItem('credentials');
    // Keep src_app to remember platform choice
    // Redirect the user to the login page or show a logout message
  }
}