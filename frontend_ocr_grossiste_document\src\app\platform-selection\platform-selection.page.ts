import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';

@Component({
  selector: 'app-platform-selection',
  templateUrl: './platform-selection.page.html',
  styleUrls: ['./platform-selection.page.scss'],
})
export class PlatformSelectionPage implements OnInit {

  hasRememberedPlatform = false;
  rememberedPlatformName = '';

  constructor(private navCtrl: NavController) { }

  ngOnInit() {
    this.checkRememberedPlatform();
  }

  checkRememberedPlatform() {
    const srcApp = localStorage.getItem('src_app');
    if (srcApp) {
      this.hasRememberedPlatform = true;
      this.rememberedPlatformName = srcApp === 'winplus' ? 'WinPlus Pharma' : 'Pharmalien';
    }
  }

  selectPlatform(platform: 'winplus' | 'pharmalien') {
    // Store the selected platform
    localStorage.setItem('src_app', platform);
    
    // Navigate to login with platform parameter
    this.navCtrl.navigateForward('/login', {
      queryParams: { platform: platform }
    });
  }

  goToRememberedPlatform() {
    const srcApp = localStorage.getItem('src_app');
    if (srcApp) {
      this.navCtrl.navigateForward('/login', {
        queryParams: { platform: srcApp }
      });
    }
  }
}
