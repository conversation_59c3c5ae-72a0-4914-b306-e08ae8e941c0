{"ast": null, "code": "var _PublicGuard;\nimport { jwtDecode } from 'jwt-decode';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class PublicGuard {\n  constructor(router) {\n    this.router = router;\n  }\n  canActivate() {\n    const tokenUser = localStorage.getItem('tokenUser');\n    const tokenTenant = localStorage.getItem('tokenTenant');\n    const tokenLocal = localStorage.getItem('token');\n    const srcApp = localStorage.getItem('src_app');\n    // Check required tokens based on platform\n    const hasRequiredTokens = srcApp === 'pharmalier' ? tokenUser && tokenLocal // Pharmalier only needs user and local tokens\n    : tokenUser && tokenTenant && tokenLocal; // WinPlusPharma needs all three tokens\n    if (hasRequiredTokens) {\n      try {\n        // Check if required tokens are valid based on platform\n        const isTokenUserValid = this.checkTokenExpiration(tokenUser);\n        const isTokenLocalValid = this.checkTokenExpiration(tokenLocal);\n        let allTokensValid = false;\n        if (srcApp === 'pharmalier') {\n          // Pharmalier only needs user and local token validation\n          allTokensValid = isTokenUserValid && isTokenLocalValid;\n        } else {\n          // WinPlusPharma needs all three token validations\n          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant);\n          allTokensValid = isTokenUserValid && isTokenTenantValid && isTokenLocalValid;\n        }\n        if (allTokensValid) {\n          // If user is already authenticated, redirect to scan-bl\n          this.router.navigate(['/scan-bl']);\n          return false;\n        }\n      } catch (error) {\n        console.error('Token validation error:', error);\n      }\n    }\n    // Allow access to public route if not authenticated\n    return true;\n  }\n  checkTokenExpiration(token) {\n    const decodedToken = jwtDecode(token);\n    const expiration = moment((decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.exp) * 1000);\n    return moment(new Date()) < expiration;\n  }\n}\n_PublicGuard = PublicGuard;\n_PublicGuard.ɵfac = function PublicGuard_Factory(t) {\n  return new (t || _PublicGuard)(i0.ɵɵinject(i1.Router));\n};\n_PublicGuard.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _PublicGuard,\n  factory: _PublicGuard.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["jwtDecode", "moment", "PublicGuard", "constructor", "router", "canActivate", "tokenUser", "localStorage", "getItem", "tokenTenant", "tokenLocal", "srcApp", "hasRequiredTokens", "isTokenUserValid", "checkTokenExpiration", "isTokenLocalValid", "allTokensValid", "isTokenTenantValid", "navigate", "error", "console", "token", "decodedToken", "expiration", "exp", "Date", "i0", "ɵɵinject", "i1", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\interceptors\\public.guard.ts"], "sourcesContent": ["// public.guard.ts\r\nimport { Injectable } from '@angular/core';\r\nimport { CanActivate, Router } from '@angular/router';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport * as moment from 'moment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PublicGuard implements CanActivate {\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  canActivate(): boolean {\r\n    const tokenUser = localStorage.getItem('tokenUser');\r\n    const tokenTenant = localStorage.getItem('tokenTenant');\r\n    const tokenLocal = localStorage.getItem('token');\r\n    const srcApp = localStorage.getItem('src_app');\r\n\r\n    // Check required tokens based on platform\r\n    const hasRequiredTokens = srcApp === 'pharmalier'\r\n      ? (tokenUser && tokenLocal) // Pharmalier only needs user and local tokens\r\n      : (tokenUser && tokenTenant && tokenLocal); // WinPlusPharma needs all three tokens\r\n\r\n    if (hasRequiredTokens) {\r\n      try {\r\n        // Check if required tokens are valid based on platform\r\n        const isTokenUserValid = this.checkTokenExpiration(tokenUser!);\r\n        const isTokenLocalValid = this.checkTokenExpiration(tokenLocal!);\r\n\r\n        let allTokensValid = false;\r\n\r\n        if (srcApp === 'pharmalier') {\r\n          // Pharmalier only needs user and local token validation\r\n          allTokensValid = isTokenUserValid && isTokenLocalValid;\r\n        } else {\r\n          // WinPlusPharma needs all three token validations\r\n          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant!);\r\n          allTokensValid = isTokenUserValid && isTokenTenantValid && isTokenLocalValid;\r\n        }\r\n\r\n        if (allTokensValid) {\r\n          // If user is already authenticated, redirect to scan-bl\r\n          this.router.navigate(['/scan-bl']);\r\n          return false;\r\n        }\r\n      } catch (error) {\r\n        console.error('Token validation error:', error);\r\n      }\r\n    }\r\n\r\n    // Allow access to public route if not authenticated\r\n    return true;\r\n  }\r\n\r\n  private checkTokenExpiration(token: string): boolean {\r\n    const decodedToken: any = jwtDecode(token);\r\n    const expiration = moment(decodedToken?.exp * 1000);\r\n    return moment(new Date()) < expiration;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,SAASA,SAAS,QAAQ,YAAY;AACtC,OAAO,KAAKC,MAAM,MAAM,QAAQ;;;AAKhC,OAAM,MAAOC,WAAW;EAEtBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErCC,WAAWA,CAAA;IACT,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACvD,MAAME,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMG,MAAM,GAAGJ,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAE9C;IACA,MAAMI,iBAAiB,GAAGD,MAAM,KAAK,YAAY,GAC5CL,SAAS,IAAII,UAAU,CAAE;IAAA,EACzBJ,SAAS,IAAIG,WAAW,IAAIC,UAAW,CAAC,CAAC;IAE9C,IAAIE,iBAAiB,EAAE;MACrB,IAAI;QACF;QACA,MAAMC,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACR,SAAU,CAAC;QAC9D,MAAMS,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAACJ,UAAW,CAAC;QAEhE,IAAIM,cAAc,GAAG,KAAK;QAE1B,IAAIL,MAAM,KAAK,YAAY,EAAE;UAC3B;UACAK,cAAc,GAAGH,gBAAgB,IAAIE,iBAAiB;SACvD,MAAM;UACL;UACA,MAAME,kBAAkB,GAAG,IAAI,CAACH,oBAAoB,CAACL,WAAY,CAAC;UAClEO,cAAc,GAAGH,gBAAgB,IAAII,kBAAkB,IAAIF,iBAAiB;;QAG9E,IAAIC,cAAc,EAAE;UAClB;UACA,IAAI,CAACZ,MAAM,CAACc,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;UAClC,OAAO,KAAK;;OAEf,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;;IAInD;IACA,OAAO,IAAI;EACb;EAEQL,oBAAoBA,CAACO,KAAa;IACxC,MAAMC,YAAY,GAAQtB,SAAS,CAACqB,KAAK,CAAC;IAC1C,MAAME,UAAU,GAAGtB,MAAM,CAAC,CAAAqB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,GAAG,IAAG,IAAI,CAAC;IACnD,OAAOvB,MAAM,CAAC,IAAIwB,IAAI,EAAE,CAAC,GAAGF,UAAU;EACxC;;eAlDWrB,WAAW;;mBAAXA,YAAW,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA;AAAA;;SAAX3B,YAAW;EAAA4B,OAAA,EAAX5B,YAAW,CAAA6B,IAAA;EAAAC,UAAA,EAFV;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}