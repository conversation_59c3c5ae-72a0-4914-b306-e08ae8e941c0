// public.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class PublicGuard implements CanActivate {

  constructor(private router: Router) {}

  canActivate(): boolean {
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const tokenLocal = localStorage.getItem('token');
    const srcApp = localStorage.getItem('src_app');

    // Check required tokens based on platform
    const hasRequiredTokens = srcApp === 'pharmalier'
      ? (tokenUser && tokenLocal) // Pharmalier only needs user and local tokens
      : (tokenUser && tokenTenant && tokenLocal); // WinPlusPharma needs all three tokens

    if (hasRequiredTokens) {
      try {
        // Check if required tokens are valid based on platform
        const isTokenUserValid = this.checkTokenExpiration(tokenUser!);
        const isTokenLocalValid = this.checkTokenExpiration(tokenLocal!);

        let allTokensValid = false;

        if (srcApp === 'pharmalier') {
          // Pharmalier only needs user and local token validation
          allTokensValid = isTokenUserValid && isTokenLocalValid;
        } else {
          // WinPlusPharma needs all three token validations
          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant!);
          allTokensValid = isTokenUserValid && isTokenTenantValid && isTokenLocalValid;
        }

        if (allTokensValid) {
          // If user is already authenticated, redirect to scan-bl
          this.router.navigate(['/scan-bl']);
          return false;
        }
      } catch (error) {
        console.error('Token validation error:', error);
      }
    }

    // Allow access to public route if not authenticated
    return true;
  }

  private checkTokenExpiration(token: string): boolean {
    const decodedToken: any = jwtDecode(token);
    const expiration = moment(decodedToken?.exp * 1000);
    return moment(new Date()) < expiration;
  }
}
