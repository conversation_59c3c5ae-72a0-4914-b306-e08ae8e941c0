{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/Work __Abder<PERSON>mane_ouhna/OCR_DOCUMENT_GROSSISTE/Frontend ocr grossiste document/frontend_ocr_grossiste_document/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _LoginPage;\nimport { environment } from '../../environments/environment';\nimport { Keyboard } from '@capacitor/keyboard';\nimport { retry, catchError } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"../services/api.service\";\nimport * as i3 from \"../services/storage.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  \"active\": a0\n});\nfunction LoginPage_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"span\", 17);\n    i0.ɵɵtext(3, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 18);\n    i0.ɵɵtext(5, \"Pharmacie\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"div\", 19);\n    i0.ɵɵelementStart(7, \"div\", 16)(8, \"span\", 17);\n    i0.ɵɵtext(9, \"2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 18);\n    i0.ɵɵtext(11, \"Utilisateur\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r0.showTenantLogin));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, !ctx_r0.showTenantLogin));\n  }\n}\nfunction LoginPage_h1_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h1\");\n    i0.ɵɵtext(1, \"Connexion Pharmalier\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginPage_h1_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h1\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.showTenantLogin ? \"Connexion de la pharmacie\" : \"Connexion de l'utilisateur\");\n  }\n}\nfunction LoginPage_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Utilisez vos identifiants Pharmalier\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginPage_p_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Vous devez utiliser \");\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3, \"les identifiants de WinPlusPharma\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginPage_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"ion-item\", 21)(2, \"ion-input\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_17_Template_ion_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.tenantUsername, $event) || (ctx_r0.tenantUsername = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"ion-item\", 21)(4, \"ion-input\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_17_Template_ion_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.tenantPassword, $event) || (ctx_r0.tenantPassword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-button\", 24);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_17_Template_ion_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.tenantLogin());\n    });\n    i0.ɵɵtext(6, \"Se connecter\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.tenantUsername);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.tenantPassword);\n  }\n}\nfunction LoginPage_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"ion-item\", 21)(2, \"ion-input\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_18_Template_ion_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.username, $event) || (ctx_r0.username = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"ion-item\", 21)(4, \"ion-input\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginPage_div_18_Template_ion_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.password, $event) || (ctx_r0.password = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-button\", 24);\n    i0.ɵɵlistener(\"click\", function LoginPage_div_18_Template_ion_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.userLogin());\n    });\n    i0.ɵɵtext(6, \"Se connecter\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.username);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.isPharmalierLogin ? \"Identifiant Pharmalier\" : \"Identifiant de l'utilisateur\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.password);\n    i0.ɵɵproperty(\"placeholder\", ctx_r0.isPharmalierLogin ? \"Mot de passe Pharmalier\" : \"Mot de passe de l'utilisateur\");\n  }\n}\nfunction LoginPage_ion_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 27);\n    i0.ɵɵlistener(\"click\", function LoginPage_ion_button_19_Template_ion_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goBackToTenantLogin());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 28);\n    i0.ɵɵtext(2, \" Retour \\u00E0 la connexion de la pharmacie \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginPage {\n  constructor(navCtrl, apiService, toastController, loadingController, storageService, route, platform) {\n    this.navCtrl = navCtrl;\n    this.apiService = apiService;\n    this.toastController = toastController;\n    this.loadingController = loadingController;\n    this.storageService = storageService;\n    this.route = route;\n    this.platform = platform;\n    this.tenantUsername = '';\n    this.tenantPassword = '';\n    this.username = '';\n    this.password = '';\n    this.showTenantLogin = true;\n    this.tenantToken = '';\n    this.hasSeenOnboarding = false;\n    this.localCredentials = {};\n    // Platform selection\n    this.currentPlatform = 'winpluspharma';\n    this.isPharmalierLogin = false;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Check platform from route parameters or localStorage\n      _this.route.queryParams.subscribe(params => {\n        if (params['platform']) {\n          _this.currentPlatform = params['platform'];\n          localStorage.setItem('src_app', _this.currentPlatform);\n        } else {\n          // Check localStorage for remembered platform\n          const savedPlatform = localStorage.getItem('src_app');\n          if (savedPlatform) {\n            _this.currentPlatform = savedPlatform;\n          } else {\n            // No platform selected, redirect to platform selection\n            _this.navCtrl.navigateRoot('/platform-selection');\n            return;\n          }\n        }\n        _this.isPharmalierLogin = _this.currentPlatform === 'pharmalier';\n        // Set up form based on platform\n        if (_this.isPharmalierLogin) {\n          _this.showTenantLogin = false; // Pharmalier doesn't need tenant login\n        } else {\n          _this.showTenantLogin = true; // WinPlusPharma needs tenant login\n        }\n      });\n      if (environment.production) {\n        _this.tenantUsername = '';\n        _this.tenantPassword = '';\n        _this.username = '';\n        _this.password = '';\n      } else {\n        _this.tenantUsername = '0001';\n        _this.tenantPassword = '123456';\n        _this.username = 'PH';\n        _this.password = 'PH';\n      }\n      // Load the credentials from the local storage\n      _this.localCredentials = yield JSON.parse(localStorage.getItem('credentials'));\n      if (_this.localCredentials) {\n        _this.tenantUsername = _this.localCredentials.tenantUsername;\n        _this.tenantPassword = _this.localCredentials.tenantPassword;\n        _this.username = _this.localCredentials.username;\n        _this.password = _this.localCredentials.password;\n      }\n      // Only add keyboard listeners on mobile platforms\n      if (_this.platform.is('capacitor')) {\n        Keyboard.addListener('keyboardWillShow', () => {\n          const footer = document.querySelector('ion-footer');\n          footer === null || footer === void 0 || footer.classList.add('keyboard-open');\n        });\n        Keyboard.addListener('keyboardWillHide', () => {\n          const footer = document.querySelector('ion-footer');\n          footer === null || footer === void 0 || footer.classList.remove('keyboard-open');\n        });\n      }\n      // Initialize storage\n      yield _this.storageService.init();\n      // Check if the user has seen onboarding\n      _this.hasSeenOnboarding = yield _this.storageService.get('hasSeenOnboarding');\n    })();\n  }\n  ngOnDestroy() {\n    // Only remove keyboard listeners on mobile platforms\n    if (this.platform.is('capacitor')) {\n      Keyboard.removeAllListeners();\n    }\n  }\n  tenantLogin() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Check if email and password are empty\n      if (_this2.tenantUsername === '' || _this2.tenantPassword === '') {\n        const toast = _this2.toastController.create({\n          message: 'Veuillez remplir les champs de connexion.',\n          duration: 2000,\n          color: 'danger'\n        }).then(toast => toast.present());\n        return;\n      }\n      const loading = yield _this2.loadingController.create({\n        message: 'Connexion de la pharmacie...'\n      });\n      yield loading.present();\n      const tenantRequest = {\n        username: _this2.tenantUsername,\n        password: _this2.tenantPassword\n      };\n      _this2.apiService.tenantLogin(tenantRequest).pipe(retry(1),\n      // Retry the request once if it fails\n      catchError(error => {\n        console.log('Tenant login error:', error);\n        if (error.status === 403) {\n          const toast = _this2.toastController.create({\n            message: error.error.detail,\n            duration: 2000,\n            color: 'danger'\n          }).then(toast => toast.present());\n        } else {\n          const toast = _this2.toastController.create({\n            message: 'La connexion de la pharmacie a échoué. Veuillez réessayer.',\n            duration: 2000,\n            color: 'danger'\n          }).then(toast => toast.present());\n        }\n        console.error('Tenant login error:', error);\n        return of(null); // Return an observable with null to continue the stream\n      })).subscribe( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (response) {\n          yield loading.dismiss();\n          if (response) {\n            console.log('Tenant login response:', response);\n            _this2.tenantToken = response.accessToken;\n            _this2.showTenantLogin = false;\n            const toast = yield _this2.toastController.create({\n              message: 'Pharmacie connectée avec succès !',\n              duration: 2000,\n              color: 'success'\n            });\n            toast.present();\n          }\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  userLogin() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (_this3.isPharmalierLogin) {\n        yield _this3.pharmalierLogin();\n      } else {\n        yield _this3.winplusUserLogin();\n      }\n    })();\n  }\n  pharmalierLogin() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // Check if username and password are empty\n      if (_this4.username === '' || _this4.password === '') {\n        const toast = _this4.toastController.create({\n          message: 'Veuillez remplir les champs de connexion.',\n          duration: 2000,\n          color: 'danger'\n        }).then(toast => toast.present());\n        return;\n      }\n      const loading = yield _this4.loadingController.create({\n        message: 'Connexion Pharmalier ...'\n      });\n      yield loading.present();\n      const pharmalierRequest = {\n        username: _this4.username,\n        password: _this4.password\n      };\n      _this4.apiService.pharmalienLogin(pharmalierRequest).subscribe( /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (response) {\n          var _response$user_data, _response$local_token;\n          console.log('Pharmalien login response:', response);\n          // Store the user data in local storage (Pharmalien only needs user data and token)\n          localStorage.setItem('tokenUser', JSON.stringify((_response$user_data = response.user_data) !== null && _response$user_data !== void 0 ? _response$user_data : \"\"));\n          localStorage.setItem('token', (_response$local_token = response.local_token) !== null && _response$local_token !== void 0 ? _response$local_token : \"\");\n          localStorage.setItem('ocrMode', 'STANDARD');\n          // Store the credentials in local storage\n          _this4.localCredentials = {\n            username: _this4.username,\n            password: _this4.password,\n            platform: 'pharmalier'\n          };\n          localStorage.setItem('credentials', JSON.stringify(_this4.localCredentials));\n          const toast = yield _this4.toastController.create({\n            message: 'Connexion Pharmalier réussie!',\n            duration: 2000,\n            color: 'success'\n          });\n          toast.present();\n          yield loading.dismiss();\n          if (!_this4.hasSeenOnboarding) {\n            yield _this4.storageService.set('hasSeenOnboarding', true);\n            _this4.navCtrl.navigateRoot('/guide');\n          } else {\n            _this4.navCtrl.navigateRoot('/scan-bl');\n          }\n        });\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }(), /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (error) {\n          console.error('Pharmalier login error:', error);\n          let errorMessage = error.error.detail || 'Connexion Pharmalier échouée. Veuillez vérifier vos identifiants.';\n          errorMessage = errorMessage.replace(/^Internal server error: \\d+: /, '');\n          const toast = yield _this4.toastController.create({\n            message: errorMessage,\n            duration: 2000,\n            color: 'danger'\n          });\n          toast.present();\n          yield loading.dismiss();\n        });\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  winplusUserLogin() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      // Check if email and password are empty\n      if (_this5.username === '' || _this5.password === '') {\n        const toast = _this5.toastController.create({\n          message: 'Veuillez remplir les champs de connexion.',\n          duration: 2000,\n          color: 'danger'\n        }).then(toast => toast.present());\n        return;\n      }\n      const loading = yield _this5.loadingController.create({\n        message: 'Connexion WinPlus ...'\n      });\n      yield loading.present();\n      const userRequest = {\n        username: _this5.username,\n        password: _this5.password,\n        tenant_token: _this5.tenantToken // Include tenant token in the request\n      };\n      _this5.apiService.userLogin(userRequest, _this5.tenantToken).subscribe( /*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator(function* (response) {\n          var _response$user_data2, _response$tenant_data, _response$local_token2;\n          console.log('User login response:', response);\n          // Store the user data in local storage\n          localStorage.setItem('tokenUser', JSON.stringify((_response$user_data2 = response.user_data) !== null && _response$user_data2 !== void 0 ? _response$user_data2 : \"\"));\n          localStorage.setItem('tokenTenant', JSON.stringify((_response$tenant_data = response.tenant_data) !== null && _response$tenant_data !== void 0 ? _response$tenant_data : \"\"));\n          localStorage.setItem('token', (_response$local_token2 = response.local_token) !== null && _response$local_token2 !== void 0 ? _response$local_token2 : \"\");\n          localStorage.setItem('ocrMode', 'STANDARD');\n          // Store the credentials in local storage\n          _this5.localCredentials = {\n            tenantUsername: _this5.tenantUsername,\n            tenantPassword: _this5.tenantPassword,\n            username: _this5.username,\n            password: _this5.password,\n            platform: 'winpluspharma'\n          };\n          localStorage.setItem('credentials', JSON.stringify(_this5.localCredentials));\n          const toast = yield _this5.toastController.create({\n            message: 'Connexion WinPlus réussie!',\n            duration: 2000,\n            color: 'success'\n          });\n          toast.present();\n          yield loading.dismiss();\n          if (!_this5.hasSeenOnboarding) {\n            yield _this5.storageService.set('hasSeenOnboarding', true);\n            _this5.navCtrl.navigateRoot('/guide');\n          } else {\n            _this5.navCtrl.navigateRoot('/scan-bl');\n          }\n        });\n        return function (_x4) {\n          return _ref4.apply(this, arguments);\n        };\n      }(), /*#__PURE__*/function () {\n        var _ref5 = _asyncToGenerator(function* (error) {\n          console.error('Login error:', error);\n          // Extract the error message without \"Internal server error: 400:\"\n          let errorMessage = error.error.detail || 'Connexion échouée. Veuillez vérifier vos identifiants.';\n          // Remove \"Internal server error: 400:\" pattern from the message\n          errorMessage = errorMessage.replace(/^Internal server error: \\d+: /, '');\n          const toast = yield _this5.toastController.create({\n            message: errorMessage,\n            duration: 2000,\n            color: 'danger'\n          });\n          toast.present();\n          yield loading.dismiss();\n        });\n        return function (_x5) {\n          return _ref5.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  goBackToTenantLogin() {\n    this.showTenantLogin = true;\n    this.tenantToken = '';\n  }\n  switchPlatform() {\n    this.navCtrl.navigateRoot('/platform-selection');\n  }\n  getPlatformDisplayName() {\n    return this.isPharmalierLogin ? 'Pharmalier' : 'WinPlus Pharma';\n  }\n}\n_LoginPage = LoginPage;\n_LoginPage.ɵfac = function LoginPage_Factory(t) {\n  return new (t || _LoginPage)(i0.ɵɵdirectiveInject(i1.NavController), i0.ɵɵdirectiveInject(i2.ApiService), i0.ɵɵdirectiveInject(i1.ToastController), i0.ɵɵdirectiveInject(i1.LoadingController), i0.ɵɵdirectiveInject(i3.StorageService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Platform));\n};\n_LoginPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _LoginPage,\n  selectors: [[\"app-login\"]],\n  decls: 27,\n  vars: 10,\n  consts: [[3, \"fullscreen\"], [1, \"login-wrapper\"], [\"size\", \"12\", 1, \"login-content\"], [1, \"platform-indicator\"], [1, \"platform-badge\"], [1, \"platform-name\"], [\"fill\", \"clear\", \"size\", \"small\", 3, \"click\"], [\"name\", \"swap-horizontal-outline\", \"slot\", \"icon-only\"], [\"class\", \"step-indicators\", 4, \"ngIf\"], [1, \"content-login-head\"], [4, \"ngIf\"], [\"class\", \"inputs-login\", 4, \"ngIf\"], [\"fill\", \"clear\", \"class\", \"back-button\", 3, \"click\", 4, \"ngIf\"], [\"src\", \"/assets/sophatel_logo.svg\", \"alt\", \"SOPHATEL Logo\", 1, \"logo\"], [1, \"copyright\", \"text-center\"], [1, \"step-indicators\"], [1, \"step-badge\", 3, \"ngClass\"], [1, \"step-number\"], [1, \"step-label\"], [1, \"step-line\"], [1, \"inputs-login\"], [\"lines\", \"none\", 1, \"input-item\"], [\"type\", \"text\", \"placeholder\", \"Identifiant de la pharmacie\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", \"placeholder\", \"Mot de passe de la pharmacie\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"expand\", \"block\", 1, \"login-button\", 3, \"click\"], [\"type\", \"text\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\"], [\"type\", \"password\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\"], [\"fill\", \"clear\", 1, \"back-button\", 3, \"click\"], [\"name\", \"arrow-back-outline\", \"slot\", \"start\"]],\n  template: function LoginPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"ion-header\");\n      i0.ɵɵelementStart(1, \"ion-content\", 0)(2, \"div\", 1)(3, \"ion-row\")(4, \"ion-col\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"span\", 5);\n      i0.ɵɵtext(8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"ion-button\", 6);\n      i0.ɵɵlistener(\"click\", function LoginPage_Template_ion_button_click_9_listener() {\n        return ctx.switchPlatform();\n      });\n      i0.ɵɵelement(10, \"ion-icon\", 7);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(11, LoginPage_div_11_Template, 12, 6, \"div\", 8);\n      i0.ɵɵelementStart(12, \"div\", 9);\n      i0.ɵɵtemplate(13, LoginPage_h1_13_Template, 2, 0, \"h1\", 10)(14, LoginPage_h1_14_Template, 2, 1, \"h1\", 10)(15, LoginPage_p_15_Template, 2, 0, \"p\", 10)(16, LoginPage_p_16_Template, 4, 0, \"p\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(17, LoginPage_div_17_Template, 7, 2, \"div\", 11)(18, LoginPage_div_18_Template, 7, 4, \"div\", 11)(19, LoginPage_ion_button_19_Template, 3, 0, \"ion-button\", 12);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(20, \"ion-footer\");\n      i0.ɵɵelement(21, \"img\", 13);\n      i0.ɵɵelementStart(22, \"p\", 14)(23, \"span\");\n      i0.ɵɵtext(24, \" Sophatel Ing\\u00E9nierie \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(25, \"br\");\n      i0.ɵɵtext(26, \"WinDoc \\u00A9 2025 - Tous droits r\\u00E9serv\\u00E9s.\");\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(7);\n      i0.ɵɵtextInterpolate(ctx.getPlatformDisplayName());\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPharmalierLogin);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isPharmalierLogin);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPharmalierLogin);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isPharmalierLogin);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPharmalierLogin);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPharmalierLogin && ctx.showTenantLogin);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isPharmalierLogin || !ctx.isPharmalierLogin && !ctx.showTenantLogin);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isPharmalierLogin && !ctx.showTenantLogin);\n    }\n  },\n  dependencies: [i5.NgClass, i5.NgIf, i6.NgControlStatus, i6.RequiredValidator, i6.NgModel, i1.IonButton, i1.IonCol, i1.IonContent, i1.IonFooter, i1.IonHeader, i1.IonIcon, i1.IonInput, i1.IonItem, i1.IonRow, i1.TextValueAccessor],\n  styles: [\"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap);@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];0[_ngcontent-%COMP%], 900[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 100[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 200[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 300[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 400[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 500[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 600[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 700[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 800[_ngcontent-%COMP%];1[_ngcontent-%COMP%], 900&display=swap)[_ngcontent-%COMP%];*[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\", sans-serif;\\n  font-optical-sizing: auto;\\n}\\n\\nion-content[_ngcontent-%COMP%]::part(scroll) {\\n  scrollbar-width: none !important;\\n  -ms-overflow-style: none !important;\\n}\\n\\n.login-wrapper[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/bg-welcome.png\\\") no-repeat center center fixed;\\n  background-size: cover;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  position: relative;\\n  padding-bottom: 80px;\\n}\\n\\n.content-login-head[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-direction: column;\\n  margin-bottom: 30px;\\n}\\n\\n.content-login-head[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #1f41bb;\\n  font-size: 34px;\\n  font-weight: bold;\\n  text-shadow: 0px 0px 1.5px #1f41bb;\\n  text-align: center;\\n}\\n\\n.content-login-head[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #141414;\\n  font-size: 18px;\\n  font-weight: 600;\\n  text-align: center;\\n  text-shadow: 0px 0px 0.5px #050505;\\n}\\n\\nion-toolbar[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --ion-color-primary: #2f4fcd;\\n}\\n\\nion-button.login-button[_ngcontent-%COMP%] {\\n  --background: #1f41bb;\\n  --background-activated: #1f41bb;\\n  --border-radius: 10px;\\n  text-align: center;\\n  box-shadow: 0px 16px 20px rgb(203, 214, 255); \\n\\n  color: #fff;\\n  font-size: 20px;\\n  font-weight: bold;\\n}\\n\\nion-button[_ngcontent-%COMP%]::part(native) {\\n  --padding-top: 20px !important;\\n  --padding-bottom: 20px !important;\\n}\\n\\n  ion-col {\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.inputs-login[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 30px;\\n  flex-direction: column;\\n  width: 85%;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n  .login-content {\\n  margin-bottom: 5rem;\\n}\\n\\n  .login-content ion-item {\\n  width: 100%;\\n  padding-right: 50px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #050505;\\n  text-align: left;\\n  padding: 8px 10px;\\n  border: 2px solid #1f41bb;\\n  border-radius: 10px;\\n  --background: #f1f4ff; \\n\\n  background-color: #f1f4ff;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding-right: 50px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1f41bb;\\n  text-align: right;\\n  text-decoration: none;\\n  margin-bottom: 20px;\\n}\\n\\nion-footer[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  background: rgba(255, 255, 255, 0.9);\\n  padding: 10px;\\n  z-index: 999;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  transition: transform 0.3s ease-out;\\n}\\nion-footer.keyboard-open[_ngcontent-%COMP%] {\\n  transform: translateY(100%);\\n}\\n\\n.copyright[_ngcontent-%COMP%] {\\n  color: #1f41bb;\\n  padding-top: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  text-shadow: 0px 0px 1px #1f41bb;\\n  text-align: center;\\n}\\n.copyright[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: bold;\\n}\\n\\n.platform-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 20px;\\n  margin-top: 10px;\\n}\\n\\n.platform-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.platform-badge[_ngcontent-%COMP%]   .platform-name[_ngcontent-%COMP%] {\\n  color: white;\\n  font-weight: 600;\\n  margin-right: 8px;\\n  text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.5);\\n}\\n.platform-badge[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\\n  --color: white;\\n  margin: 0;\\n  --padding-start: 8px;\\n  --padding-end: 8px;\\n}\\n\\n.step-indicators[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  margin-top: 10px;\\n  width: 100%;\\n  max-width: 300px;\\n}\\n\\n.step-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.step-badge[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: #f1f4ff;\\n  border: 2px solid #1f41bb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #1f41bb;\\n  font-weight: bold;\\n  font-size: 18px;\\n  transition: all 0.3s ease;\\n}\\n.step-badge[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  color: #1f41bb;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.step-badge[_ngcontent-%COMP%]:hover {\\n  cursor: pointer;\\n  transform: translateY(-2px);\\n}\\n.step-badge.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background-color: #1f41bb;\\n  color: white;\\n  transform: scale(1.1);\\n}\\n.step-badge.active[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.step-line[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background-color: #1f41bb;\\n  margin: 0 15px;\\n  margin-bottom: 20px;\\n  max-width: 100px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["environment", "Keyboard", "retry", "catchError", "of", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r0", "showTenant<PERSON><PERSON>in", "ɵɵtextInterpolate", "ɵɵtwoWayListener", "LoginPage_div_17_Template_ion_input_ngModelChange_2_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "tenantUsername", "ɵɵresetView", "LoginPage_div_17_Template_ion_input_ngModelChange_4_listener", "tenantPassword", "ɵɵlistener", "LoginPage_div_17_Template_ion_button_click_5_listener", "tenantLogin", "ɵɵtwoWayProperty", "LoginPage_div_18_Template_ion_input_ngModelChange_2_listener", "_r3", "username", "LoginPage_div_18_Template_ion_input_ngModelChange_4_listener", "password", "LoginPage_div_18_Template_ion_button_click_5_listener", "userLogin", "isPharmalierLogin", "LoginPage_ion_button_19_Template_ion_button_click_0_listener", "_r4", "goBackToTenantLogin", "LoginPage", "constructor", "navCtrl", "apiService", "toastController", "loadingController", "storageService", "route", "platform", "tenantToken", "hasSeenOnboarding", "localCredentials", "currentPlatform", "ngOnInit", "_this", "_asyncToGenerator", "queryParams", "subscribe", "params", "localStorage", "setItem", "savedPlatform", "getItem", "navigateRoot", "production", "JSON", "parse", "is", "addListener", "footer", "document", "querySelector", "classList", "add", "remove", "init", "get", "ngOnDestroy", "removeAllListeners", "_this2", "toast", "create", "message", "duration", "color", "then", "present", "loading", "tenantRequest", "pipe", "error", "console", "log", "status", "detail", "_ref", "response", "dismiss", "accessToken", "_x", "apply", "arguments", "_this3", "pharmalierLogin", "winplusUserLogin", "_this4", "pharmalierRequest", "pharmalien<PERSON><PERSON>in", "_ref2", "_response$user_data", "_response$local_token", "stringify", "user_data", "local_token", "set", "_x2", "_ref3", "errorMessage", "replace", "_x3", "_this5", "userRequest", "tenant_token", "_ref4", "_response$user_data2", "_response$tenant_data", "_response$local_token2", "tenant_data", "_x4", "_ref5", "_x5", "switchPlatform", "getPlatformDisplayName", "ɵɵdirectiveInject", "i1", "NavController", "i2", "ApiService", "ToastController", "LoadingController", "i3", "StorageService", "i4", "ActivatedRoute", "Platform", "selectors", "decls", "vars", "consts", "template", "LoginPage_Template", "rf", "ctx", "LoginPage_Template_ion_button_click_9_listener", "ɵɵtemplate", "LoginPage_div_11_Template", "LoginPage_h1_13_Template", "LoginPage_h1_14_Template", "LoginPage_p_15_Template", "LoginPage_p_16_Template", "LoginPage_div_17_Template", "LoginPage_div_18_Template", "LoginPage_ion_button_19_Template"], "sources": ["C:\\Users\\<USER>\\Downloads\\<PERSON> __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\login\\login.page.ts", "C:\\Users\\<USER>\\Downloads\\<PERSON> __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\login\\login.page.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { NavController, ToastController, LoadingController, Platform  } from '@ionic/angular';\r\nimport { ApiService } from '../services/api.service';\r\nimport { LoginRequest, LoginResponse, TenantLoginRequest, TenantLoginResponse, PharmalienLoginRequest, PharmalienLoginResponse } from '../../models/login';\r\nimport { environment } from '../../environments/environment';\r\nimport { Keyboard } from '@capacitor/keyboard';\r\nimport { retry, catchError } from 'rxjs/operators';\r\nimport { of } from 'rxjs';\r\nimport { StorageService } from '../services/storage.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.page.html',\r\n  styleUrls: ['./login.page.scss'],\r\n})\r\nexport class LoginPage implements OnInit {\r\n  tenantUsername: string = '';\r\n  tenantPassword: string = '';\r\n  username: string = '';\r\n  password: string = '';\r\n  showTenantLogin: boolean = true;\r\n  tenantToken: string = '';\r\n  hasSeenOnboarding: boolean = false;\r\n  localCredentials: any = {};\r\n\r\n  // Platform selection\r\n  currentPlatform: 'winpluspharma' | 'pharmalier' = 'winpluspharma';\r\n  isPharmalierLogin: boolean = false;\r\n\r\n  constructor(\r\n    private navCtrl: NavController,\r\n    private apiService: ApiService,\r\n    private toastController: ToastController,\r\n    private loadingController: LoadingController,\r\n    private storageService: StorageService,\r\n    private route: ActivatedRoute,\r\n    private platform: Platform\r\n  ) {}\r\n\r\n  async ngOnInit() {\r\n    // Check platform from route parameters or localStorage\r\n    this.route.queryParams.subscribe(params => {\r\n      if (params['platform']) {\r\n        this.currentPlatform = params['platform'];\r\n        localStorage.setItem('src_app', this.currentPlatform);\r\n      } else {\r\n        // Check localStorage for remembered platform\r\n        const savedPlatform = localStorage.getItem('src_app');\r\n        if (savedPlatform) {\r\n          this.currentPlatform = savedPlatform as 'winpluspharma' | 'pharmalier';\r\n        } else {\r\n          // No platform selected, redirect to platform selection\r\n          this.navCtrl.navigateRoot('/platform-selection');\r\n          return;\r\n        }\r\n      }\r\n\r\n      this.isPharmalierLogin = this.currentPlatform === 'pharmalier';\r\n\r\n      // Set up form based on platform\r\n      if (this.isPharmalierLogin) {\r\n        this.showTenantLogin = false; // Pharmalier doesn't need tenant login\r\n      } else {\r\n        this.showTenantLogin = true; // WinPlusPharma needs tenant login\r\n      }\r\n    });\r\n\r\n    if (environment.production) {\r\n      this.tenantUsername = '';\r\n      this.tenantPassword = '';\r\n      this.username = '';\r\n      this.password = '';\r\n    } else {\r\n      this.tenantUsername = '0001';\r\n      this.tenantPassword = '123456';\r\n      this.username = 'PH';\r\n      this.password = 'PH';\r\n    }\r\n\r\n    // Load the credentials from the local storage\r\n    this.localCredentials = await JSON.parse(localStorage.getItem('credentials') as any);\r\n    if (this.localCredentials) {\r\n      this.tenantUsername = this.localCredentials.tenantUsername;\r\n      this.tenantPassword = this.localCredentials.tenantPassword\r\n      this.username = this.localCredentials.username;\r\n      this.password = this.localCredentials.password;\r\n    }\r\n\r\n    // Only add keyboard listeners on mobile platforms\r\n    if (this.platform.is('capacitor')) {\r\n      Keyboard.addListener('keyboardWillShow', () => {\r\n        const footer = document.querySelector('ion-footer');\r\n        footer?.classList.add('keyboard-open');\r\n      });\r\n\r\n      Keyboard.addListener('keyboardWillHide', () => {\r\n        const footer = document.querySelector('ion-footer');\r\n        footer?.classList.remove('keyboard-open');\r\n      });\r\n    }\r\n\r\n    // Initialize storage\r\n    await this.storageService.init();\r\n  \r\n    // Check if the user has seen onboarding\r\n    this.hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');\r\n  }\r\n\r\n\r\n  ngOnDestroy() {\r\n    // Only remove keyboard listeners on mobile platforms\r\n    if (this.platform.is('capacitor')) {\r\n      Keyboard.removeAllListeners();\r\n    }\r\n  }\r\n\r\n  async tenantLogin() {\r\n\r\n    // Check if email and password are empty\r\n    if (this.tenantUsername === '' || this.tenantPassword === '') {\r\n      const toast = this.toastController.create({\r\n        message: 'Veuillez remplir les champs de connexion.',\r\n        duration: 2000,\r\n        color: 'danger',\r\n      }).then(toast => toast.present());\r\n      return;\r\n    }\r\n\r\n    const loading = await this.loadingController.create({\r\n      message: 'Connexion de la pharmacie...',\r\n    });\r\n    await loading.present();\r\n  \r\n    const tenantRequest: TenantLoginRequest = {\r\n      username: this.tenantUsername,\r\n      password: this.tenantPassword,\r\n    };\r\n  \r\n    this.apiService.tenantLogin(tenantRequest)\r\n      .pipe(\r\n        retry(1), // Retry the request once if it fails\r\n        catchError(error => {\r\n          console.log('Tenant login error:', error);\r\n          \r\n          if(error.status === 403){\r\n              const toast =  this.toastController.create({\r\n                message: error.error.detail,\r\n                duration: 2000,\r\n                color: 'danger',\r\n              }).then(toast => toast.present());\r\n          }\r\n          else{\r\n            const toast = this.toastController.create({\r\n              message: 'La connexion de la pharmacie a échoué. Veuillez réessayer.',\r\n              duration: 2000,\r\n              color: 'danger',\r\n            }).then(toast => toast.present());\r\n          }\r\n          console.error('Tenant login error:', error);\r\n          return of(null); // Return an observable with null to continue the stream\r\n        })\r\n      )\r\n      .subscribe(\r\n        async (response: TenantLoginResponse | null) => {\r\n          await loading.dismiss();\r\n          if (response) {\r\n            console.log('Tenant login response:', response);\r\n            this.tenantToken = response.accessToken;\r\n            this.showTenantLogin = false;\r\n            const toast = await this.toastController.create({\r\n              message: 'Pharmacie connectée avec succès !',\r\n              duration: 2000,\r\n              color: 'success',\r\n            });\r\n            toast.present();\r\n          }\r\n        }\r\n      );\r\n  }\r\n\r\n  async userLogin() {\r\n    if (this.isPharmalierLogin) {\r\n      await this.pharmalierLogin();\r\n    } else {\r\n      await this.winplusUserLogin();\r\n    }\r\n  }\r\n\r\n  async pharmalierLogin() {\r\n    // Check if username and password are empty\r\n    if (this.username === '' || this.password === '') {\r\n      const toast = this.toastController.create({\r\n        message: 'Veuillez remplir les champs de connexion.',\r\n        duration: 2000,\r\n        color: 'danger',\r\n      }).then(toast => toast.present());\r\n      return;\r\n    }\r\n\r\n    const loading = await this.loadingController.create({\r\n      message: 'Connexion Pharmalier ...',\r\n    });\r\n    await loading.present();\r\n\r\n    const pharmalierRequest: PharmalienLoginRequest = {\r\n      username: this.username,\r\n      password: this.password\r\n    };\r\n\r\n    this.apiService.pharmalienLogin(pharmalierRequest).subscribe(\r\n      async (response: PharmalienLoginResponse) => {\r\n        console.log('Pharmalien login response:', response);\r\n\r\n        // Store the user data in local storage (Pharmalien only needs user data and token)\r\n        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? \"\"));\r\n        localStorage.setItem('token', response.local_token ?? \"\");\r\n        localStorage.setItem('ocrMode', 'STANDARD');\r\n\r\n        // Store the credentials in local storage\r\n        this.localCredentials = {\r\n          username: this.username,\r\n          password: this.password,\r\n          platform: 'pharmalier'\r\n        };\r\n        localStorage.setItem('credentials', JSON.stringify(this.localCredentials));\r\n\r\n        const toast = await this.toastController.create({\r\n          message: 'Connexion Pharmalier réussie!',\r\n          duration: 2000,\r\n          color: 'success',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n\r\n        if (!this.hasSeenOnboarding) {\r\n          await this.storageService.set('hasSeenOnboarding', true);\r\n          this.navCtrl.navigateRoot('/guide');\r\n        } else {\r\n          this.navCtrl.navigateRoot('/scan-bl');\r\n        }\r\n      },\r\n      async (error) => {\r\n        console.error('Pharmalier login error:', error);\r\n\r\n        let errorMessage = error.error.detail || 'Connexion Pharmalier échouée. Veuillez vérifier vos identifiants.';\r\n        errorMessage = errorMessage.replace(/^Internal server error: \\d+: /, '');\r\n\r\n        const toast = await this.toastController.create({\r\n          message: errorMessage,\r\n          duration: 2000,\r\n          color: 'danger',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  async winplusUserLogin() {\r\n\r\n    // Check if email and password are empty\r\n    if (this.username === '' || this.password === '') {\r\n      const toast = this.toastController.create({\r\n        message: 'Veuillez remplir les champs de connexion.',\r\n        duration: 2000,\r\n        color: 'danger',\r\n      }).then(toast => toast.present());\r\n      return;\r\n    }\r\n\r\n    const loading = await this.loadingController.create({\r\n      message: 'Connexion WinPlus ...',\r\n    });\r\n    await loading.present();\r\n\r\n    const userRequest: LoginRequest = {\r\n      username: this.username,\r\n      password: this.password,\r\n      tenant_token: this.tenantToken  // Include tenant token in the request\r\n    };\r\n\r\n    this.apiService.userLogin(userRequest, this.tenantToken).subscribe(\r\n      async (response: LoginResponse) => {\r\n        console.log('User login response:', response);\r\n        \r\n        // Store the user data in local storage\r\n        localStorage.setItem('tokenUser', JSON.stringify(response.user_data ?? \"\"));\r\n        localStorage.setItem('tokenTenant', JSON.stringify(response.tenant_data ?? \"\"));\r\n        localStorage.setItem('token', response.local_token ?? \"\");\r\n        localStorage.setItem('ocrMode', 'STANDARD');\r\n\r\n        // Store the credentials in local storage\r\n        this.localCredentials = {\r\n          tenantUsername: this.tenantUsername,\r\n          tenantPassword: this.tenantPassword,\r\n          username: this.username,\r\n          password: this.password,\r\n          platform: 'winpluspharma'\r\n        };\r\n        localStorage.setItem('credentials', JSON.stringify(this.localCredentials));\r\n        \r\n        const toast = await this.toastController.create({\r\n          message: 'Connexion WinPlus réussie!',\r\n          duration: 2000,\r\n          color: 'success',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n\r\n        if (!this.hasSeenOnboarding) {\r\n          await this.storageService.set('hasSeenOnboarding', true);\r\n          this.navCtrl.navigateRoot('/guide');\r\n        }\r\n        else{\r\n          this.navCtrl.navigateRoot('/scan-bl');\r\n        }\r\n      },\r\n      async (error) => {\r\n        console.error('Login error:', error);\r\n      \r\n        // Extract the error message without \"Internal server error: 400:\"\r\n        let errorMessage = error.error.detail || 'Connexion échouée. Veuillez vérifier vos identifiants.';\r\n        \r\n        // Remove \"Internal server error: 400:\" pattern from the message\r\n        errorMessage = errorMessage.replace(/^Internal server error: \\d+: /, '');\r\n\r\n        const toast = await this.toastController.create({\r\n          message: errorMessage,\r\n          duration: 2000,\r\n          color: 'danger',\r\n        });\r\n        toast.present();\r\n        await loading.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  goBackToTenantLogin() {\r\n    this.showTenantLogin = true;\r\n    this.tenantToken = '';\r\n  }\r\n\r\n  switchPlatform() {\r\n    this.navCtrl.navigateRoot('/platform-selection');\r\n  }\r\n\r\n  getPlatformDisplayName(): string {\r\n    return this.isPharmalierLogin ? 'Pharmalier' : 'WinPlus Pharma';\r\n  }\r\n}", "<ion-header>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"login-wrapper\">\r\n    <ion-row>\r\n      <ion-col size=\"12\" class=\"login-content\">\r\n        <!-- Platform indicator -->\r\n        <div class=\"platform-indicator\">\r\n          <div class=\"platform-badge\">\r\n            <span class=\"platform-name\">{{ getPlatformDisplayName() }}</span>\r\n            <ion-button fill=\"clear\" size=\"small\" (click)=\"switchPlatform()\">\r\n              <ion-icon name=\"swap-horizontal-outline\" slot=\"icon-only\"></ion-icon>\r\n            </ion-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step indicators for WinPlus only -->\r\n        <div class=\"step-indicators\" *ngIf=\"!isPharmalierLogin\">\r\n          <div class=\"step-badge\" [ngClass]=\"{'active': showTenantLogin}\">\r\n            <span class=\"step-number\">1</span>\r\n            <span class=\"step-label\">Pharmacie</span>\r\n          </div>\r\n          <div class=\"step-line\"></div>\r\n          <div class=\"step-badge\" [ngClass]=\"{'active': !showTenantLogin}\">\r\n            <span class=\"step-number\">2</span>\r\n            <span class=\"step-label\">Utilisateur</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"content-login-head\">\r\n          <h1 *ngIf=\"isPharmalierLogin\">Connexion Pharmalier</h1>\r\n          <h1 *ngIf=\"!isPharmalierLogin\">{{ showTenantLogin ? 'Connexion de la pharmacie' : 'Connexion de l\\'utilisateur' }}</h1>\r\n          <p *ngIf=\"isPharmalierLogin\">Utilisez vos identifiants Pharmalier</p>\r\n          <p *ngIf=\"!isPharmalierLogin\">Vous devez utiliser <br>les identifiants de WinPlusPharma</p>\r\n        </div>\r\n\r\n        <!-- WinPlus tenant login (step 1) -->\r\n        <div class=\"inputs-login\" *ngIf=\"!isPharmalierLogin && showTenantLogin\">\r\n          <ion-item lines=\"none\" class=\"input-item\">\r\n            <ion-input [(ngModel)]=\"tenantUsername\" type=\"text\" placeholder=\"Identifiant de la pharmacie\" required></ion-input>\r\n          </ion-item>\r\n\r\n          <ion-item lines=\"none\" class=\"input-item\">\r\n            <ion-input [(ngModel)]=\"tenantPassword\" type=\"password\" placeholder=\"Mot de passe de la pharmacie\" required></ion-input>\r\n          </ion-item>\r\n          <ion-button expand=\"block\" class=\"login-button\" (click)=\"tenantLogin()\">Se connecter</ion-button>\r\n        </div>\r\n\r\n        <!-- User login (WinPlus step 2 or Pharmalien single step) -->\r\n        <div class=\"inputs-login\" *ngIf=\"isPharmalierLogin || (!isPharmalierLogin && !showTenantLogin)\">\r\n          <ion-item lines=\"none\" class=\"input-item\">\r\n            <ion-input [(ngModel)]=\"username\" type=\"text\" [placeholder]=\"isPharmalierLogin ? 'Identifiant Pharmalier' : 'Identifiant de l\\'utilisateur'\" required></ion-input>\r\n          </ion-item>\r\n\r\n          <ion-item lines=\"none\" class=\"input-item\">\r\n            <ion-input [(ngModel)]=\"password\" type=\"password\" [placeholder]=\"isPharmalierLogin ? 'Mot de passe Pharmalier' : 'Mot de passe de l\\'utilisateur'\" required></ion-input>\r\n          </ion-item>\r\n          <ion-button expand=\"block\" class=\"login-button\" (click)=\"userLogin()\">Se connecter</ion-button>\r\n        </div>\r\n\r\n\r\n\r\n        <!-- Back button for WinPlus only -->\r\n        <ion-button fill=\"clear\" class=\"back-button\" (click)=\"goBackToTenantLogin()\" *ngIf=\"!isPharmalierLogin && !showTenantLogin\">\r\n          <ion-icon name=\"arrow-back-outline\" slot=\"start\"></ion-icon>\r\n          Retour à la connexion de la pharmacie\r\n        </ion-button>\r\n      </ion-col>\r\n    </ion-row>\r\n\r\n\r\n  </div>\r\n</ion-content>\r\n<ion-footer>\r\n  <img src=\"/assets/sophatel_logo.svg\" alt=\"SOPHATEL Logo\" class=\"logo\">\r\n  <p class=\"copyright text-center\"><span> Sophatel Ingénierie </span> <br>WinDoc © 2025 - Tous droits réservés.</p> \r\n</ion-footer>"], "mappings": ";;AAIA,SAASA,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,KAAK,EAAEC,UAAU,QAAQ,gBAAgB;AAClD,SAASC,EAAE,QAAQ,MAAM;;;;;;;;;;;;;ICabC,EAFJ,CAAAC,cAAA,cAAwD,cACU,eACpC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IACpCF,EADoC,CAAAG,YAAA,EAAO,EACrC;IACNH,EAAA,CAAAI,SAAA,cAA6B;IAE3BJ,EADF,CAAAC,cAAA,cAAiE,eACrC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAExCF,EAFwC,CAAAG,YAAA,EAAO,EACvC,EACF;;;;IAToBH,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,eAAA,EAAuC;IAKvCV,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,eAAA,EAAwC;;;;;IAMhEV,EAAA,CAAAC,cAAA,SAA8B;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACvDH,EAAA,CAAAC,cAAA,SAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAmF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAxFH,EAAA,CAAAK,SAAA,EAAmF;IAAnFL,EAAA,CAAAW,iBAAA,CAAAF,MAAA,CAAAC,eAAA,8DAAmF;;;;;IAClHV,EAAA,CAAAC,cAAA,QAA6B;IAAAD,EAAA,CAAAE,MAAA,2CAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACrEH,EAAA,CAAAC,cAAA,QAA8B;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAI,SAAA,SAAI;IAAAJ,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAMzFH,EAFJ,CAAAC,cAAA,cAAwE,mBAC5B,oBAC+D;IAA5FD,EAAA,CAAAY,gBAAA,2BAAAC,6DAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAU,cAAA,EAAAL,MAAA,MAAAL,MAAA,CAAAU,cAAA,GAAAL,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAA4B;IACzCd,EADyG,CAAAG,YAAA,EAAY,EAC1G;IAGTH,EADF,CAAAC,cAAA,mBAA0C,oBACoE;IAAjGD,EAAA,CAAAY,gBAAA,2BAAAS,6DAAAP,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAa,cAAA,EAAAR,MAAA,MAAAL,MAAA,CAAAa,cAAA,GAAAR,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAA4B;IACzCd,EAD8G,CAAAG,YAAA,EAAY,EAC/G;IACXH,EAAA,CAAAC,cAAA,qBAAwE;IAAxBD,EAAA,CAAAuB,UAAA,mBAAAC,sDAAA;MAAAxB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASX,MAAA,CAAAgB,WAAA,EAAa;IAAA,EAAC;IAACzB,EAAA,CAAAE,MAAA,mBAAY;IACtFF,EADsF,CAAAG,YAAA,EAAa,EAC7F;;;;IAPSH,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAU,cAAA,CAA4B;IAI5BnB,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAa,cAAA,CAA4B;;;;;;IAQvCtB,EAFJ,CAAAC,cAAA,cAAgG,mBACpD,oBAC8G;IAA3ID,EAAA,CAAAY,gBAAA,2BAAAe,6DAAAb,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAa,GAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAoB,QAAA,EAAAf,MAAA,MAAAL,MAAA,CAAAoB,QAAA,GAAAf,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAAsB;IACnCd,EADwJ,CAAAG,YAAA,EAAY,EACzJ;IAGTH,EADF,CAAAC,cAAA,mBAA0C,oBACoH;IAAjJD,EAAA,CAAAY,gBAAA,2BAAAkB,6DAAAhB,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAa,GAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAT,MAAA,CAAAsB,QAAA,EAAAjB,MAAA,MAAAL,MAAA,CAAAsB,QAAA,GAAAjB,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAAsB;IACnCd,EAD8J,CAAAG,YAAA,EAAY,EAC/J;IACXH,EAAA,CAAAC,cAAA,qBAAsE;IAAtBD,EAAA,CAAAuB,UAAA,mBAAAS,sDAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAa,GAAA;MAAA,MAAAnB,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASX,MAAA,CAAAwB,SAAA,EAAW;IAAA,EAAC;IAACjC,EAAA,CAAAE,MAAA,mBAAY;IACpFF,EADoF,CAAAG,YAAA,EAAa,EAC3F;;;;IAPSH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAoB,QAAA,CAAsB;IAAa7B,EAAA,CAAAM,UAAA,gBAAAG,MAAA,CAAAyB,iBAAA,6DAA8F;IAIjIlC,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAsB,QAAA,CAAsB;IAAiB/B,EAAA,CAAAM,UAAA,gBAAAG,MAAA,CAAAyB,iBAAA,+DAAgG;;;;;;IAQtJlC,EAAA,CAAAC,cAAA,qBAA4H;IAA/ED,EAAA,CAAAuB,UAAA,mBAAAY,6DAAA;MAAAnC,EAAA,CAAAe,aAAA,CAAAqB,GAAA;MAAA,MAAA3B,MAAA,GAAAT,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASX,MAAA,CAAA4B,mBAAA,EAAqB;IAAA,EAAC;IAC1ErC,EAAA,CAAAI,SAAA,mBAA4D;IAC5DJ,EAAA,CAAAE,MAAA,mDACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;ADlDrB,OAAM,MAAOmC,SAAS;EAcpBC,YACUC,OAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,iBAAoC,EACpCC,cAA8B,EAC9BC,KAAqB,EACrBC,QAAkB;IANlB,KAAAN,OAAO,GAAPA,OAAO;IACP,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IApBlB,KAAA3B,cAAc,GAAW,EAAE;IAC3B,KAAAG,cAAc,GAAW,EAAE;IAC3B,KAAAO,QAAQ,GAAW,EAAE;IACrB,KAAAE,QAAQ,GAAW,EAAE;IACrB,KAAArB,eAAe,GAAY,IAAI;IAC/B,KAAAqC,WAAW,GAAW,EAAE;IACxB,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,gBAAgB,GAAQ,EAAE;IAE1B;IACA,KAAAC,eAAe,GAAmC,eAAe;IACjE,KAAAhB,iBAAiB,GAAY,KAAK;EAU/B;EAEGiB,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ;MACAD,KAAI,CAACP,KAAK,CAACS,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE;UACtBJ,KAAI,CAACF,eAAe,GAAGM,MAAM,CAAC,UAAU,CAAC;UACzCC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAEN,KAAI,CAACF,eAAe,CAAC;SACtD,MAAM;UACL;UACA,MAAMS,aAAa,GAAGF,YAAY,CAACG,OAAO,CAAC,SAAS,CAAC;UACrD,IAAID,aAAa,EAAE;YACjBP,KAAI,CAACF,eAAe,GAAGS,aAA+C;WACvE,MAAM;YACL;YACAP,KAAI,CAACZ,OAAO,CAACqB,YAAY,CAAC,qBAAqB,CAAC;YAChD;;;QAIJT,KAAI,CAAClB,iBAAiB,GAAGkB,KAAI,CAACF,eAAe,KAAK,YAAY;QAE9D;QACA,IAAIE,KAAI,CAAClB,iBAAiB,EAAE;UAC1BkB,KAAI,CAAC1C,eAAe,GAAG,KAAK,CAAC,CAAC;SAC/B,MAAM;UACL0C,KAAI,CAAC1C,eAAe,GAAG,IAAI,CAAC,CAAC;;MAEjC,CAAC,CAAC;MAEF,IAAIf,WAAW,CAACmE,UAAU,EAAE;QAC1BV,KAAI,CAACjC,cAAc,GAAG,EAAE;QACxBiC,KAAI,CAAC9B,cAAc,GAAG,EAAE;QACxB8B,KAAI,CAACvB,QAAQ,GAAG,EAAE;QAClBuB,KAAI,CAACrB,QAAQ,GAAG,EAAE;OACnB,MAAM;QACLqB,KAAI,CAACjC,cAAc,GAAG,MAAM;QAC5BiC,KAAI,CAAC9B,cAAc,GAAG,QAAQ;QAC9B8B,KAAI,CAACvB,QAAQ,GAAG,IAAI;QACpBuB,KAAI,CAACrB,QAAQ,GAAG,IAAI;;MAGtB;MACAqB,KAAI,CAACH,gBAAgB,SAASc,IAAI,CAACC,KAAK,CAACP,YAAY,CAACG,OAAO,CAAC,aAAa,CAAQ,CAAC;MACpF,IAAIR,KAAI,CAACH,gBAAgB,EAAE;QACzBG,KAAI,CAACjC,cAAc,GAAGiC,KAAI,CAACH,gBAAgB,CAAC9B,cAAc;QAC1DiC,KAAI,CAAC9B,cAAc,GAAG8B,KAAI,CAACH,gBAAgB,CAAC3B,cAAc;QAC1D8B,KAAI,CAACvB,QAAQ,GAAGuB,KAAI,CAACH,gBAAgB,CAACpB,QAAQ;QAC9CuB,KAAI,CAACrB,QAAQ,GAAGqB,KAAI,CAACH,gBAAgB,CAAClB,QAAQ;;MAGhD;MACA,IAAIqB,KAAI,CAACN,QAAQ,CAACmB,EAAE,CAAC,WAAW,CAAC,EAAE;QACjCrE,QAAQ,CAACsE,WAAW,CAAC,kBAAkB,EAAE,MAAK;UAC5C,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;UACnDF,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEG,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;QACxC,CAAC,CAAC;QAEF3E,QAAQ,CAACsE,WAAW,CAAC,kBAAkB,EAAE,MAAK;UAC5C,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;UACnDF,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEG,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;QAC3C,CAAC,CAAC;;MAGJ;MACA,MAAMpB,KAAI,CAACR,cAAc,CAAC6B,IAAI,EAAE;MAEhC;MACArB,KAAI,CAACJ,iBAAiB,SAASI,KAAI,CAACR,cAAc,CAAC8B,GAAG,CAAC,mBAAmB,CAAC;IAAC;EAC9E;EAGAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC7B,QAAQ,CAACmB,EAAE,CAAC,WAAW,CAAC,EAAE;MACjCrE,QAAQ,CAACgF,kBAAkB,EAAE;;EAEjC;EAEMnD,WAAWA,CAAA;IAAA,IAAAoD,MAAA;IAAA,OAAAxB,iBAAA;MAEf;MACA,IAAIwB,MAAI,CAAC1D,cAAc,KAAK,EAAE,IAAI0D,MAAI,CAACvD,cAAc,KAAK,EAAE,EAAE;QAC5D,MAAMwD,KAAK,GAAGD,MAAI,CAACnC,eAAe,CAACqC,MAAM,CAAC;UACxCC,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;QACjC;;MAGF,MAAMC,OAAO,SAASR,MAAI,CAAClC,iBAAiB,CAACoC,MAAM,CAAC;QAClDC,OAAO,EAAE;OACV,CAAC;MACF,MAAMK,OAAO,CAACD,OAAO,EAAE;MAEvB,MAAME,aAAa,GAAuB;QACxCzD,QAAQ,EAAEgD,MAAI,CAAC1D,cAAc;QAC7BY,QAAQ,EAAE8C,MAAI,CAACvD;OAChB;MAEDuD,MAAI,CAACpC,UAAU,CAAChB,WAAW,CAAC6D,aAAa,CAAC,CACvCC,IAAI,CACH1F,KAAK,CAAC,CAAC,CAAC;MAAE;MACVC,UAAU,CAAC0F,KAAK,IAAG;QACjBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,KAAK,CAAC;QAEzC,IAAGA,KAAK,CAACG,MAAM,KAAK,GAAG,EAAC;UACpB,MAAMb,KAAK,GAAID,MAAI,CAACnC,eAAe,CAACqC,MAAM,CAAC;YACzCC,OAAO,EAAEQ,KAAK,CAACA,KAAK,CAACI,MAAM;YAC3BX,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;SACpC,MACG;UACF,MAAMN,KAAK,GAAGD,MAAI,CAACnC,eAAe,CAACqC,MAAM,CAAC;YACxCC,OAAO,EAAE,4DAA4D;YACrEC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;;QAEnCK,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,OAAOzF,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CACH,CACAwD,SAAS;QAAA,IAAAsC,IAAA,GAAAxC,iBAAA,CACR,WAAOyC,QAAoC,EAAI;UAC7C,MAAMT,OAAO,CAACU,OAAO,EAAE;UACvB,IAAID,QAAQ,EAAE;YACZL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,QAAQ,CAAC;YAC/CjB,MAAI,CAAC9B,WAAW,GAAG+C,QAAQ,CAACE,WAAW;YACvCnB,MAAI,CAACnE,eAAe,GAAG,KAAK;YAC5B,MAAMoE,KAAK,SAASD,MAAI,CAACnC,eAAe,CAACqC,MAAM,CAAC;cAC9CC,OAAO,EAAE,mCAAmC;cAC5CC,QAAQ,EAAE,IAAI;cACdC,KAAK,EAAE;aACR,CAAC;YACFJ,KAAK,CAACM,OAAO,EAAE;;QAEnB,CAAC;QAAA,iBAAAa,EAAA;UAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;QAAA;MAAA,IACF;IAAC;EACN;EAEMlE,SAASA,CAAA;IAAA,IAAAmE,MAAA;IAAA,OAAA/C,iBAAA;MACb,IAAI+C,MAAI,CAAClE,iBAAiB,EAAE;QAC1B,MAAMkE,MAAI,CAACC,eAAe,EAAE;OAC7B,MAAM;QACL,MAAMD,MAAI,CAACE,gBAAgB,EAAE;;IAC9B;EACH;EAEMD,eAAeA,CAAA;IAAA,IAAAE,MAAA;IAAA,OAAAlD,iBAAA;MACnB;MACA,IAAIkD,MAAI,CAAC1E,QAAQ,KAAK,EAAE,IAAI0E,MAAI,CAACxE,QAAQ,KAAK,EAAE,EAAE;QAChD,MAAM+C,KAAK,GAAGyB,MAAI,CAAC7D,eAAe,CAACqC,MAAM,CAAC;UACxCC,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;QACjC;;MAGF,MAAMC,OAAO,SAASkB,MAAI,CAAC5D,iBAAiB,CAACoC,MAAM,CAAC;QAClDC,OAAO,EAAE;OACV,CAAC;MACF,MAAMK,OAAO,CAACD,OAAO,EAAE;MAEvB,MAAMoB,iBAAiB,GAA2B;QAChD3E,QAAQ,EAAE0E,MAAI,CAAC1E,QAAQ;QACvBE,QAAQ,EAAEwE,MAAI,CAACxE;OAChB;MAEDwE,MAAI,CAAC9D,UAAU,CAACgE,eAAe,CAACD,iBAAiB,CAAC,CAACjD,SAAS;QAAA,IAAAmD,KAAA,GAAArD,iBAAA,CAC1D,WAAOyC,QAAiC,EAAI;UAAA,IAAAa,mBAAA,EAAAC,qBAAA;UAC1CnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;UAEnD;UACArC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEK,IAAI,CAAC8C,SAAS,EAAAF,mBAAA,GAACb,QAAQ,CAACgB,SAAS,cAAAH,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC,CAAC;UAC3ElD,YAAY,CAACC,OAAO,CAAC,OAAO,GAAAkD,qBAAA,GAAEd,QAAQ,CAACiB,WAAW,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UACzDnD,YAAY,CAACC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;UAE3C;UACA6C,MAAI,CAACtD,gBAAgB,GAAG;YACtBpB,QAAQ,EAAE0E,MAAI,CAAC1E,QAAQ;YACvBE,QAAQ,EAAEwE,MAAI,CAACxE,QAAQ;YACvBe,QAAQ,EAAE;WACX;UACDW,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEK,IAAI,CAAC8C,SAAS,CAACN,MAAI,CAACtD,gBAAgB,CAAC,CAAC;UAE1E,MAAM6B,KAAK,SAASyB,MAAI,CAAC7D,eAAe,CAACqC,MAAM,CAAC;YAC9CC,OAAO,EAAE,+BAA+B;YACxCC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACU,OAAO,EAAE;UAEvB,IAAI,CAACQ,MAAI,CAACvD,iBAAiB,EAAE;YAC3B,MAAMuD,MAAI,CAAC3D,cAAc,CAACoE,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;YACxDT,MAAI,CAAC/D,OAAO,CAACqB,YAAY,CAAC,QAAQ,CAAC;WACpC,MAAM;YACL0C,MAAI,CAAC/D,OAAO,CAACqB,YAAY,CAAC,UAAU,CAAC;;QAEzC,CAAC;QAAA,iBAAAoD,GAAA;UAAA,OAAAP,KAAA,CAAAR,KAAA,OAAAC,SAAA;QAAA;MAAA;QAAA,IAAAe,KAAA,GAAA7D,iBAAA,CACD,WAAOmC,KAAK,EAAI;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAE/C,IAAI2B,YAAY,GAAG3B,KAAK,CAACA,KAAK,CAACI,MAAM,IAAI,mEAAmE;UAC5GuB,YAAY,GAAGA,YAAY,CAACC,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC;UAExE,MAAMtC,KAAK,SAASyB,MAAI,CAAC7D,eAAe,CAACqC,MAAM,CAAC;YAC9CC,OAAO,EAAEmC,YAAY;YACrBlC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACU,OAAO,EAAE;QACzB,CAAC;QAAA,iBAAAsB,GAAA;UAAA,OAAAH,KAAA,CAAAhB,KAAA,OAAAC,SAAA;QAAA;MAAA,IACF;IAAC;EACJ;EAEMG,gBAAgBA,CAAA;IAAA,IAAAgB,MAAA;IAAA,OAAAjE,iBAAA;MAEpB;MACA,IAAIiE,MAAI,CAACzF,QAAQ,KAAK,EAAE,IAAIyF,MAAI,CAACvF,QAAQ,KAAK,EAAE,EAAE;QAChD,MAAM+C,KAAK,GAAGwC,MAAI,CAAC5E,eAAe,CAACqC,MAAM,CAAC;UACxCC,OAAO,EAAE,2CAA2C;UACpDC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,EAAE,CAAC;QACjC;;MAGF,MAAMC,OAAO,SAASiC,MAAI,CAAC3E,iBAAiB,CAACoC,MAAM,CAAC;QAClDC,OAAO,EAAE;OACV,CAAC;MACF,MAAMK,OAAO,CAACD,OAAO,EAAE;MAEvB,MAAMmC,WAAW,GAAiB;QAChC1F,QAAQ,EAAEyF,MAAI,CAACzF,QAAQ;QACvBE,QAAQ,EAAEuF,MAAI,CAACvF,QAAQ;QACvByF,YAAY,EAAEF,MAAI,CAACvE,WAAW,CAAE;OACjC;MAEDuE,MAAI,CAAC7E,UAAU,CAACR,SAAS,CAACsF,WAAW,EAAED,MAAI,CAACvE,WAAW,CAAC,CAACQ,SAAS;QAAA,IAAAkE,KAAA,GAAApE,iBAAA,CAChE,WAAOyC,QAAuB,EAAI;UAAA,IAAA4B,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;UAChCnC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,QAAQ,CAAC;UAE7C;UACArC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEK,IAAI,CAAC8C,SAAS,EAAAa,oBAAA,GAAC5B,QAAQ,CAACgB,SAAS,cAAAY,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC,CAAC;UAC3EjE,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEK,IAAI,CAAC8C,SAAS,EAAAc,qBAAA,GAAC7B,QAAQ,CAAC+B,WAAW,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,CAAC;UAC/ElE,YAAY,CAACC,OAAO,CAAC,OAAO,GAAAkE,sBAAA,GAAE9B,QAAQ,CAACiB,WAAW,cAAAa,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;UACzDnE,YAAY,CAACC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;UAE3C;UACA4D,MAAI,CAACrE,gBAAgB,GAAG;YACtB9B,cAAc,EAAEmG,MAAI,CAACnG,cAAc;YACnCG,cAAc,EAAEgG,MAAI,CAAChG,cAAc;YACnCO,QAAQ,EAAEyF,MAAI,CAACzF,QAAQ;YACvBE,QAAQ,EAAEuF,MAAI,CAACvF,QAAQ;YACvBe,QAAQ,EAAE;WACX;UACDW,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEK,IAAI,CAAC8C,SAAS,CAACS,MAAI,CAACrE,gBAAgB,CAAC,CAAC;UAE1E,MAAM6B,KAAK,SAASwC,MAAI,CAAC5E,eAAe,CAACqC,MAAM,CAAC;YAC9CC,OAAO,EAAE,4BAA4B;YACrCC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACU,OAAO,EAAE;UAEvB,IAAI,CAACuB,MAAI,CAACtE,iBAAiB,EAAE;YAC3B,MAAMsE,MAAI,CAAC1E,cAAc,CAACoE,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;YACxDM,MAAI,CAAC9E,OAAO,CAACqB,YAAY,CAAC,QAAQ,CAAC;WACpC,MACG;YACFyD,MAAI,CAAC9E,OAAO,CAACqB,YAAY,CAAC,UAAU,CAAC;;QAEzC,CAAC;QAAA,iBAAAiE,GAAA;UAAA,OAAAL,KAAA,CAAAvB,KAAA,OAAAC,SAAA;QAAA;MAAA;QAAA,IAAA4B,KAAA,GAAA1E,iBAAA,CACD,WAAOmC,KAAK,EAAI;UACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UAEpC;UACA,IAAI2B,YAAY,GAAG3B,KAAK,CAACA,KAAK,CAACI,MAAM,IAAI,wDAAwD;UAEjG;UACAuB,YAAY,GAAGA,YAAY,CAACC,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC;UAExE,MAAMtC,KAAK,SAASwC,MAAI,CAAC5E,eAAe,CAACqC,MAAM,CAAC;YAC9CC,OAAO,EAAEmC,YAAY;YACrBlC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC;UACFJ,KAAK,CAACM,OAAO,EAAE;UACf,MAAMC,OAAO,CAACU,OAAO,EAAE;QACzB,CAAC;QAAA,iBAAAiC,GAAA;UAAA,OAAAD,KAAA,CAAA7B,KAAA,OAAAC,SAAA;QAAA;MAAA,IACF;IAAC;EACJ;EAEA9D,mBAAmBA,CAAA;IACjB,IAAI,CAAC3B,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACqC,WAAW,GAAG,EAAE;EACvB;EAEAkF,cAAcA,CAAA;IACZ,IAAI,CAACzF,OAAO,CAACqB,YAAY,CAAC,qBAAqB,CAAC;EAClD;EAEAqE,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAChG,iBAAiB,GAAG,YAAY,GAAG,gBAAgB;EACjE;;aA7UWI,SAAS;;mBAATA,UAAS,EAAAtC,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAArI,EAAA,CAAAmI,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAvI,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAI,eAAA,GAAAxI,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAK,iBAAA,GAAAzI,EAAA,CAAAmI,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA3I,EAAA,CAAAmI,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA7I,EAAA,CAAAmI,iBAAA,CAAAC,EAAA,CAAAU,QAAA;AAAA;;QAATxG,UAAS;EAAAyG,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MChBtBrJ,EAAA,CAAAI,SAAA,iBACa;MASDJ,EAPZ,CAAAC,cAAA,qBAAiC,aACJ,cAChB,iBACkC,aAEP,aACF,cACE;MAAAD,EAAA,CAAAE,MAAA,GAA8B;MAAAF,EAAA,CAAAG,YAAA,EAAO;MACjEH,EAAA,CAAAC,cAAA,oBAAiE;MAA3BD,EAAA,CAAAuB,UAAA,mBAAAgI,+CAAA;QAAA,OAASD,GAAA,CAAArB,cAAA,EAAgB;MAAA,EAAC;MAC9DjI,EAAA,CAAAI,SAAA,mBAAqE;MAG3EJ,EAFI,CAAAG,YAAA,EAAa,EACT,EACF;MAGNH,EAAA,CAAAwJ,UAAA,KAAAC,yBAAA,kBAAwD;MAWxDzJ,EAAA,CAAAC,cAAA,cAAgC;MAI9BD,EAHA,CAAAwJ,UAAA,KAAAE,wBAAA,iBAA8B,KAAAC,wBAAA,iBACC,KAAAC,uBAAA,gBACF,KAAAC,uBAAA,gBACC;MAChC7J,EAAA,CAAAG,YAAA,EAAM;MA6BNH,EA1BA,CAAAwJ,UAAA,KAAAM,yBAAA,kBAAwE,KAAAC,yBAAA,kBAYwB,KAAAC,gCAAA,yBAc4B;MASpIhK,EALM,CAAAG,YAAA,EAAU,EACF,EAGN,EACM;MACdH,EAAA,CAAAC,cAAA,kBAAY;MACVD,EAAA,CAAAI,SAAA,eAAsE;MACrCJ,EAAjC,CAAAC,cAAA,aAAiC,YAAM;MAACD,EAAA,CAAAE,MAAA,kCAAoB;MAAAF,EAAA,CAAAG,YAAA,EAAO;MAACH,EAAA,CAAAI,SAAA,UAAI;MAAAJ,EAAA,CAAAE,MAAA,4DAAqC;MAC/GF,EAD+G,CAAAG,YAAA,EAAI,EACtG;;;MAzEAH,EAAA,CAAAK,SAAA,EAAmB;MAAnBL,EAAA,CAAAM,UAAA,oBAAmB;MAOQN,EAAA,CAAAK,SAAA,GAA8B;MAA9BL,EAAA,CAAAW,iBAAA,CAAA2I,GAAA,CAAApB,sBAAA,GAA8B;MAQhClI,EAAA,CAAAK,SAAA,GAAwB;MAAxBL,EAAA,CAAAM,UAAA,UAAAgJ,GAAA,CAAApH,iBAAA,CAAwB;MAY/ClC,EAAA,CAAAK,SAAA,GAAuB;MAAvBL,EAAA,CAAAM,UAAA,SAAAgJ,GAAA,CAAApH,iBAAA,CAAuB;MACvBlC,EAAA,CAAAK,SAAA,EAAwB;MAAxBL,EAAA,CAAAM,UAAA,UAAAgJ,GAAA,CAAApH,iBAAA,CAAwB;MACzBlC,EAAA,CAAAK,SAAA,EAAuB;MAAvBL,EAAA,CAAAM,UAAA,SAAAgJ,GAAA,CAAApH,iBAAA,CAAuB;MACvBlC,EAAA,CAAAK,SAAA,EAAwB;MAAxBL,EAAA,CAAAM,UAAA,UAAAgJ,GAAA,CAAApH,iBAAA,CAAwB;MAIHlC,EAAA,CAAAK,SAAA,EAA2C;MAA3CL,EAAA,CAAAM,UAAA,UAAAgJ,GAAA,CAAApH,iBAAA,IAAAoH,GAAA,CAAA5I,eAAA,CAA2C;MAY3CV,EAAA,CAAAK,SAAA,EAAmE;MAAnEL,EAAA,CAAAM,UAAA,SAAAgJ,GAAA,CAAApH,iBAAA,KAAAoH,GAAA,CAAApH,iBAAA,KAAAoH,GAAA,CAAA5I,eAAA,CAAmE;MAchBV,EAAA,CAAAK,SAAA,EAA4C;MAA5CL,EAAA,CAAAM,UAAA,UAAAgJ,GAAA,CAAApH,iBAAA,KAAAoH,GAAA,CAAA5I,eAAA,CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}