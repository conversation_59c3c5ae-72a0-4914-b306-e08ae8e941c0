{"ast": null, "code": "var _PlatformSelectionPage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nfunction PlatformSelectionPage_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"ion-button\", 13);\n    i0.ɵɵlistener(\"click\", function PlatformSelectionPage_div_34_Template_ion_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToRememberedPlatform());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Retour \\u00E0 \", ctx_r1.rememberedPlatformName, \" \");\n  }\n}\nexport class PlatformSelectionPage {\n  constructor(navCtrl) {\n    this.navCtrl = navCtrl;\n    this.hasRememberedPlatform = false;\n    this.rememberedPlatformName = '';\n  }\n  ngOnInit() {\n    this.checkRememberedPlatform();\n  }\n  checkRememberedPlatform() {\n    const srcApp = localStorage.getItem('src_app');\n    if (srcApp) {\n      this.hasRememberedPlatform = true;\n      this.rememberedPlatformName = srcApp === 'winpluspharma' ? 'WinPlus Pharma' : 'Pharmalier';\n    }\n  }\n  selectPlatform(platform) {\n    // Store the selected platform\n    localStorage.setItem('src_app', platform);\n    // Navigate to login with platform parameter\n    this.navCtrl.navigateForward('/login', {\n      queryParams: {\n        platform: platform\n      }\n    });\n  }\n  goToRememberedPlatform() {\n    const srcApp = localStorage.getItem('src_app');\n    if (srcApp) {\n      this.navCtrl.navigateForward('/login', {\n        queryParams: {\n          platform: srcApp\n        }\n      });\n    }\n  }\n}\n_PlatformSelectionPage = PlatformSelectionPage;\n_PlatformSelectionPage.ɵfac = function PlatformSelectionPage_Factory(t) {\n  return new (t || _PlatformSelectionPage)(i0.ɵɵdirectiveInject(i1.NavController));\n};\n_PlatformSelectionPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _PlatformSelectionPage,\n  selectors: [[\"app-platform-selection\"]],\n  decls: 35,\n  vars: 2,\n  consts: [[3, \"fullscreen\"], [1, \"platform-selection-wrapper\"], [\"size\", \"12\", 1, \"platform-content\"], [1, \"content-header\"], [1, \"platform-buttons\"], [1, \"platform-option\", 3, \"click\"], [1, \"platform-image-container\"], [\"src\", \"assets/onboarding_images/winpluspharm.svg\", \"alt\", \"WinPlus Pharma\", 1, \"platform-image\"], [1, \"platform-info\"], [1, \"platform-description\"], [\"src\", \"assets/onboarding_images/winpharm.svg\", \"alt\", \"Pharmalien\", 1, \"platform-image\"], [\"class\", \"switch-platform-link\", 4, \"ngIf\"], [1, \"switch-platform-link\"], [\"fill\", \"clear\", 3, \"click\"], [\"name\", \"arrow-back-outline\", \"slot\", \"start\"]],\n  template: function PlatformSelectionPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\")(1, \"ion-toolbar\")(2, \"ion-title\");\n      i0.ɵɵtext(3, \"Choisir votre plateforme\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(4, \"ion-content\", 0)(5, \"div\", 1)(6, \"ion-row\")(7, \"ion-col\", 2)(8, \"div\", 3)(9, \"h1\");\n      i0.ɵɵtext(10, \"Choisir votre plateforme\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"p\");\n      i0.ɵɵtext(12, \"S\\u00E9lectionnez la plateforme avec laquelle vous souhaitez vous connecter\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 5);\n      i0.ɵɵlistener(\"click\", function PlatformSelectionPage_Template_div_click_14_listener() {\n        return ctx.selectPlatform(\"winplus\");\n      });\n      i0.ɵɵelementStart(15, \"div\", 6);\n      i0.ɵɵelement(16, \"img\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 8)(18, \"h2\");\n      i0.ɵɵtext(19, \"WinPlus Pharma\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"p\");\n      i0.ɵɵtext(21, \"Connexion avec votre compte WinPlus\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"p\", 9);\n      i0.ɵɵtext(23, \"Authentification en deux \\u00E9tapes (Pharmacie + Utilisateur)\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(24, \"div\", 5);\n      i0.ɵɵlistener(\"click\", function PlatformSelectionPage_Template_div_click_24_listener() {\n        return ctx.selectPlatform(\"pharmalien\");\n      });\n      i0.ɵɵelementStart(25, \"div\", 6);\n      i0.ɵɵelement(26, \"img\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"div\", 8)(28, \"h2\");\n      i0.ɵɵtext(29, \"Pharmalien\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"p\");\n      i0.ɵɵtext(31, \"Connexion avec votre compte Pharmalien\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"p\", 9);\n      i0.ɵɵtext(33, \"Authentification simple (Utilisateur uniquement)\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(34, PlatformSelectionPage_div_34_Template, 4, 1, \"div\", 11);\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(30);\n      i0.ɵɵproperty(\"ngIf\", ctx.hasRememberedPlatform);\n    }\n  },\n  dependencies: [i2.NgIf, i1.IonButton, i1.IonCol, i1.IonContent, i1.IonHeader, i1.IonIcon, i1.IonRow, i1.IonTitle, i1.IonToolbar],\n  styles: [\".platform-selection-wrapper[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.platform-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  max-width: 600px;\\n  width: 100%;\\n}\\n\\n.content-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n  color: white;\\n}\\n.content-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  margin-bottom: 10px;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.content-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  opacity: 0.9;\\n  margin: 0;\\n}\\n\\n.platform-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  width: 100%;\\n  margin-bottom: 30px;\\n}\\n\\n.platform-option[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  border: 2px solid transparent;\\n}\\n.platform-option[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\\n  border-color: #667eea;\\n}\\n.platform-option[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n\\n.platform-image-container[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin-right: 20px;\\n}\\n.platform-image-container[_ngcontent-%COMP%]   .platform-image[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  object-fit: contain;\\n}\\n\\n.platform-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.platform-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n.platform-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n.platform-info[_ngcontent-%COMP%]   .platform-description[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #999;\\n  font-style: italic;\\n}\\n\\n.switch-platform-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 20px;\\n}\\n.switch-platform-link[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\\n  --color: white;\\n  font-size: 1rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .platform-option[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    padding: 20px;\\n  }\\n  .platform-option[_ngcontent-%COMP%]   .platform-image-container[_ngcontent-%COMP%] {\\n    margin-right: 0;\\n    margin-bottom: 16px;\\n  }\\n  .content-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .platform-buttons[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .platform-selection-wrapper[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .content-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .platform-option[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .platform-image-container[_ngcontent-%COMP%]   .platform-image[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "PlatformSelectionPage_div_34_Template_ion_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "goToRememberedPlatform", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "rememberedPlatformName", "PlatformSelectionPage", "constructor", "navCtrl", "hasRememberedPlatform", "ngOnInit", "checkRememberedPlatform", "srcApp", "localStorage", "getItem", "selectPlatform", "platform", "setItem", "navigateForward", "queryParams", "ɵɵdirectiveInject", "i1", "NavController", "selectors", "decls", "vars", "consts", "template", "PlatformSelectionPage_Template", "rf", "ctx", "PlatformSelectionPage_Template_div_click_14_listener", "PlatformSelectionPage_Template_div_click_24_listener", "ɵɵtemplate", "PlatformSelectionPage_div_34_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\platform-selection\\platform-selection.page.ts", "C:\\Users\\<USER>\\Downloads\\<PERSON> __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\platform-selection\\platform-selection.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { NavController } from '@ionic/angular';\n\n@Component({\n  selector: 'app-platform-selection',\n  templateUrl: './platform-selection.page.html',\n  styleUrls: ['./platform-selection.page.scss'],\n})\nexport class PlatformSelectionPage implements OnInit {\n\n  hasRememberedPlatform = false;\n  rememberedPlatformName = '';\n\n  constructor(private navCtrl: NavController) { }\n\n  ngOnInit() {\n    this.checkRememberedPlatform();\n  }\n\n  checkRememberedPlatform() {\n    const srcApp = localStorage.getItem('src_app');\n    if (srcApp) {\n      this.hasRememberedPlatform = true;\n      this.rememberedPlatformName = srcApp === 'winpluspharma' ? 'WinPlus Pharma' : 'Pharmalier';\n    }\n  }\n\n  selectPlatform(platform: 'winpluspharma' | 'pharmalier') {\n    // Store the selected platform\n    localStorage.setItem('src_app', platform);\n\n    // Navigate to login with platform parameter\n    this.navCtrl.navigateForward('/login', {\n      queryParams: { platform: platform }\n    });\n  }\n\n  goToRememberedPlatform() {\n    const srcApp = localStorage.getItem('src_app');\n    if (srcApp) {\n      this.navCtrl.navigateForward('/login', {\n        queryParams: { platform: srcApp }\n      });\n    }\n  }\n}\n", "<ion-header>\n  <ion-toolbar>\n    <ion-title>Choisir votre plateforme</ion-title>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\">\n  <div class=\"platform-selection-wrapper\">\n    <ion-row>\n      <ion-col size=\"12\" class=\"platform-content\">\n        <div class=\"content-header\">\n          <h1>Choisir votre plateforme</h1>\n          <p>Sélectionnez la plateforme avec laquelle vous souhaitez vous connecter</p>\n        </div>\n\n        <div class=\"platform-buttons\">\n          <div class=\"platform-option\" (click)=\"selectPlatform('winplus')\">\n            <div class=\"platform-image-container\">\n              <img src=\"assets/onboarding_images/winpluspharm.svg\" alt=\"WinPlus Pharma\" class=\"platform-image\">\n            </div>\n            <div class=\"platform-info\">\n              <h2>WinPlus Pharma</h2>\n              <p>Connexion avec votre compte WinPlus</p>\n              <p class=\"platform-description\">Authentification en deux étapes (Pharmacie + Utilisateur)</p>\n            </div>\n          </div>\n\n          <div class=\"platform-option\" (click)=\"selectPlatform('pharmalien')\">\n            <div class=\"platform-image-container\">\n              <img src=\"assets/onboarding_images/winpharm.svg\" alt=\"Pharmalien\" class=\"platform-image\">\n            </div>\n            <div class=\"platform-info\">\n              <h2>Pharmalien</h2>\n              <p>Connexion avec votre compte Pharmalien</p>\n              <p class=\"platform-description\">Authentification simple (Utilisateur uniquement)</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"switch-platform-link\" *ngIf=\"hasRememberedPlatform\">\n          <ion-button fill=\"clear\" (click)=\"goToRememberedPlatform()\">\n            <ion-icon name=\"arrow-back-outline\" slot=\"start\"></ion-icon>\n            Retour à {{ rememberedPlatformName }}\n          </ion-button>\n        </div>\n      </ion-col>\n    </ion-row>\n  </div>\n</ion-content>\n"], "mappings": ";;;;;;;ICwCUA,EADF,CAAAC,cAAA,cAAgE,qBACF;IAAnCD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IACzDT,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAW,MAAA,GACF;IACFX,EADE,CAAAY,YAAA,EAAa,EACT;;;;IAFFZ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,oBAAAR,MAAA,CAAAS,sBAAA,MACF;;;ADnCV,OAAM,MAAOC,qBAAqB;EAKhCC,YAAoBC,OAAsB;IAAtB,KAAAA,OAAO,GAAPA,OAAO;IAH3B,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAJ,sBAAsB,GAAG,EAAE;EAEmB;EAE9CK,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAC9C,IAAIF,MAAM,EAAE;MACV,IAAI,CAACH,qBAAqB,GAAG,IAAI;MACjC,IAAI,CAACJ,sBAAsB,GAAGO,MAAM,KAAK,eAAe,GAAG,gBAAgB,GAAG,YAAY;;EAE9F;EAEAG,cAAcA,CAACC,QAAwC;IACrD;IACAH,YAAY,CAACI,OAAO,CAAC,SAAS,EAAED,QAAQ,CAAC;IAEzC;IACA,IAAI,CAACR,OAAO,CAACU,eAAe,CAAC,QAAQ,EAAE;MACrCC,WAAW,EAAE;QAAEH,QAAQ,EAAEA;MAAQ;KAClC,CAAC;EACJ;EAEAjB,sBAAsBA,CAAA;IACpB,MAAMa,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAC9C,IAAIF,MAAM,EAAE;MACV,IAAI,CAACJ,OAAO,CAACU,eAAe,CAAC,QAAQ,EAAE;QACrCC,WAAW,EAAE;UAAEH,QAAQ,EAAEJ;QAAM;OAChC,CAAC;;EAEN;;yBApCWN,qBAAqB;;mBAArBA,sBAAqB,EAAAhB,EAAA,CAAA8B,iBAAA,CAAAC,EAAA,CAAAC,aAAA;AAAA;;QAArBhB,sBAAqB;EAAAiB,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCN9BvC,EAFJ,CAAAC,cAAA,iBAAY,kBACG,gBACA;MAAAD,EAAA,CAAAW,MAAA,+BAAwB;MAEvCX,EAFuC,CAAAY,YAAA,EAAY,EACnC,EACH;MAOHZ,EALV,CAAAC,cAAA,qBAAiC,aACS,cAC7B,iBACqC,aACd,SACtB;MAAAD,EAAA,CAAAW,MAAA,gCAAwB;MAAAX,EAAA,CAAAY,YAAA,EAAK;MACjCZ,EAAA,CAAAC,cAAA,SAAG;MAAAD,EAAA,CAAAW,MAAA,mFAAsE;MAC3EX,EAD2E,CAAAY,YAAA,EAAI,EACzE;MAGJZ,EADF,CAAAC,cAAA,cAA8B,cACqC;MAApCD,EAAA,CAAAE,UAAA,mBAAAuC,qDAAA;QAAA,OAASD,GAAA,CAAAf,cAAA,CAAe,SAAS,CAAC;MAAA,EAAC;MAC9DzB,EAAA,CAAAC,cAAA,cAAsC;MACpCD,EAAA,CAAAU,SAAA,cAAiG;MACnGV,EAAA,CAAAY,YAAA,EAAM;MAEJZ,EADF,CAAAC,cAAA,cAA2B,UACrB;MAAAD,EAAA,CAAAW,MAAA,sBAAc;MAAAX,EAAA,CAAAY,YAAA,EAAK;MACvBZ,EAAA,CAAAC,cAAA,SAAG;MAAAD,EAAA,CAAAW,MAAA,2CAAmC;MAAAX,EAAA,CAAAY,YAAA,EAAI;MAC1CZ,EAAA,CAAAC,cAAA,YAAgC;MAAAD,EAAA,CAAAW,MAAA,sEAAyD;MAE7FX,EAF6F,CAAAY,YAAA,EAAI,EACzF,EACF;MAENZ,EAAA,CAAAC,cAAA,cAAoE;MAAvCD,EAAA,CAAAE,UAAA,mBAAAwC,qDAAA;QAAA,OAASF,GAAA,CAAAf,cAAA,CAAe,YAAY,CAAC;MAAA,EAAC;MACjEzB,EAAA,CAAAC,cAAA,cAAsC;MACpCD,EAAA,CAAAU,SAAA,eAAyF;MAC3FV,EAAA,CAAAY,YAAA,EAAM;MAEJZ,EADF,CAAAC,cAAA,cAA2B,UACrB;MAAAD,EAAA,CAAAW,MAAA,kBAAU;MAAAX,EAAA,CAAAY,YAAA,EAAK;MACnBZ,EAAA,CAAAC,cAAA,SAAG;MAAAD,EAAA,CAAAW,MAAA,8CAAsC;MAAAX,EAAA,CAAAY,YAAA,EAAI;MAC7CZ,EAAA,CAAAC,cAAA,YAAgC;MAAAD,EAAA,CAAAW,MAAA,wDAAgD;MAGtFX,EAHsF,CAAAY,YAAA,EAAI,EAChF,EACF,EACF;MAENZ,EAAA,CAAA2C,UAAA,KAAAC,qCAAA,kBAAgE;MASxE5C,EAHM,CAAAY,YAAA,EAAU,EACF,EACN,EACM;;;MA1CDZ,EAAA,CAAAa,SAAA,GAAmB;MAAnBb,EAAA,CAAA6C,UAAA,oBAAmB;MAiCW7C,EAAA,CAAAa,SAAA,IAA2B;MAA3Bb,EAAA,CAAA6C,UAAA,SAAAL,GAAA,CAAArB,qBAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}