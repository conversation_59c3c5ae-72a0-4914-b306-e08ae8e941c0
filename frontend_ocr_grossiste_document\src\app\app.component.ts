import { Component, OnInit } from '@angular/core';
import { register } from 'swiper/element/bundle';
import { Router, NavigationStart } from '@angular/router';
import { ApiService } from './services/api.service';
import { Platform } from '@ionic/angular';
import { StorageService } from './services/storage.service';
import { environment } from '../environments/environment'; // Import the environment

register();

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements OnInit {
  
  // constructor(private networkService: NetworkService, private navCtrl: NavController) {
  //   this.networkService.getNetworkStatus().subscribe((connected: boolean) => {
  //     if (!connected) {
  //       this.navCtrl.navigateRoot('/network-error');
  //     }
  //   });
  // }

  environment = environment;

  

  constructor(private storageService: StorageService, private router: Router, private apiService: ApiService,private platform: Platform) {
    this.initializeApp();
  }

  initializeApp() {
    this.platform.ready().then(() => {
      this.forceLightMode();
      this.checkEnvironmentAndClearStorage();
    });
  }

  private forceLightMode() {
    // Remove dark mode from body
    document.body.classList.remove('dark');
    document.documentElement.classList.remove('dark');
    
    // Add light mode
    document.body.classList.add('light');
    document.documentElement.classList.add('light');

    // Set attribute
    document.body.setAttribute('data-theme', 'light');
    
    // Force color scheme
    const meta = document.createElement('meta');
    meta.name = 'color-scheme';
    meta.content = 'light';
    document.head.appendChild(meta);
  }

  async ngOnInit() {
    // Ensure light mode is applied
    this.forceLightMode();

    // Initialize storage
    await this.storageService.init();

    // Check if the user has seen onboarding
    const hasSeenOnboarding = await this.storageService.get('hasSeenOnboarding');

    // Only redirect to onboarding if user is on root path and hasn't seen onboarding
    const currentUrl = this.router.url;
    if (!hasSeenOnboarding && (currentUrl === '/' || currentUrl === '')) {
      // Navigate to the onboarding screen if not seen before
      this.router.navigate(['/onboarding']);
    }

    // Handle route redirection for logged-in users
    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        if (!this.apiService.isLoggedIn() && !this.isPublicRoute(event.url)) {
          this.router.navigate(['/welcome']);
        }
      }
    });
  }

  isPublicRoute(url: string): boolean {
    const publicRoutes = ['/login', '/onboarding', '/welcome', '/network-error', '/request-error'];
    return publicRoutes.includes(url);
  }


  private async checkEnvironmentAndClearStorage() {
    if (!environment.production) {
      console.log('Non-production environment detected. Clearing storage...');
      await this.storageService.clear(); // Clear all storage
    }
  }
}
