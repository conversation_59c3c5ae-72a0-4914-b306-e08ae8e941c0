// auth.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(private router: Router) {}

  canActivate(): boolean {
    const tokenUser = localStorage.getItem('tokenUser');
    const tokenTenant = localStorage.getItem('tokenTenant');
    const tokenLocal = localStorage.getItem('token');
    const srcApp = localStorage.getItem('src_app');

    // Check required tokens based on platform
    const hasRequiredTokens = srcApp === 'pharmalier'
      ? (tokenUser && tokenLocal) // Pharmalier only needs user and local tokens
      : (tokenUser && tokenTenant && tokenLocal); // WinPlusPharma needs all three tokens

    if (hasRequiredTokens) {
      try {
        // Check expiration for required tokens based on platform
        const isTokenUserValid = this.checkTokenExpiration(tokenUser!);
        const isTokenLocalValid = this.checkTokenExpiration(tokenLocal!);

        if (srcApp === 'pharmalier') {
          // Pharmalier only needs user and local token validation
          if (isTokenUserValid && isTokenLocalValid) {
            return true;
          }
        } else {
          // WinPlusPharma needs all three token validations
          const isTokenTenantValid = this.checkTokenExpiration(tokenTenant!);
          if (isTokenUserValid && isTokenTenantValid && isTokenLocalValid) {
            return true;
          }
        }
      } catch (error) {
        console.error('Token validation error:', error);
      }
    }

    // If any required token is missing or expired, redirect to welcome or login
    if (!srcApp) {
      this.router.navigate(['/welcome']);
    } else {
      this.router.navigate(['/login'], { queryParams: { platform: srcApp } });
    }
    return false;
  }

  private checkTokenExpiration(token: string): boolean {
    const decodedToken: any = jwtDecode(token);
    const expiration = moment(decodedToken?.exp * 1000);
    return moment(new Date()) < expiration;
  }
}