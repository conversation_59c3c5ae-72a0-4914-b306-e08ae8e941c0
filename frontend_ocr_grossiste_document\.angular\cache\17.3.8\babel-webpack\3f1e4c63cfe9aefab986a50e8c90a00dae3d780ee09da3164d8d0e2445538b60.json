{"ast": null, "code": "var _AppRoutingModule;\nimport { PreloadAllModules, RouterModule } from '@angular/router';\nimport { AuthGuard } from './interceptors/auth.guard';\nimport { OnboardingGuard } from './interceptors/onboardin.guard';\nimport { PublicGuard } from './interceptors/public.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'home',\n  loadChildren: () => import('./welcome/welcome.module').then(m => m.WelcomePageModule)\n}, {\n  path: '',\n  redirectTo: 'onboarding',\n  pathMatch: 'full'\n}, {\n  path: 'onboarding',\n  loadChildren: () => import('./onboarding/onboarding.module').then(m => m.OnboardingPageModule),\n  canActivate: [OnboardingGuard, PublicGuard]\n}, {\n  path: 'welcome',\n  loadChildren: () => import('./welcome/welcome.module').then(m => m.WelcomePageModule),\n  canActivate: [PublicGuard]\n}, {\n  path: 'platform-selection',\n  loadChildren: () => import('./platform-selection/platform-selection.module').then(m => m.PlatformSelectionPageModule),\n  canActivate: [PublicGuard]\n}, {\n  path: 'login',\n  loadChildren: () => import('./login/login.module').then(m => m.LoginPageModule),\n  canActivate: [PublicGuard]\n}, {\n  path: 'guide',\n  loadChildren: () => import('./guide/guide.module').then(m => m.GuidePageModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'scan-bl',\n  loadChildren: () => import('./scan-bl/scan-bl.module').then(m => m.ScanBLPageModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'doc-list',\n  loadChildren: () => import('./doc-list/doc-list.module').then(m => m.DocListPageModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'data-bl',\n  loadChildren: () => import('./data-bl/data-bl.module').then(m => m.DataBLPageModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'crop-doc',\n  loadChildren: () => import('./crop-doc/crop-doc.module').then(m => m.CropDocPageModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'process-doc',\n  loadChildren: () => import('./process-doc/process-doc.module').then(m => m.ProcessDocPageModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'network-error',\n  loadChildren: () => import('./network-error/network-error.module').then(m => m.NetworkErrorPageModule)\n}, {\n  path: 'request-error',\n  loadChildren: () => import('./request-error/request-error.module').then(m => m.RequestErrorPageModule)\n}, {\n  path: 'realtime-contours',\n  loadChildren: () => import('./realtime-contours/realtime-contours.module').then(m => m.RealtimeContoursPageModule),\n  canActivate: [AuthGuard]\n}, {\n  path: 'data-bl-success',\n  loadChildren: () => import('./data-bl-success/data-bl-success.module').then(m => m.DataBlSuccessPageModule)\n}, {\n  path: 'profile',\n  loadChildren: () => import('./profile/profile.module').then(m => m.ProfilePageModule)\n}, {\n  path: 'medicament-ocr',\n  loadChildren: () => import('./medicament-ocr/medicament-ocr.module').then(m => m.MedicamentOcrPageModule)\n}];\nexport class AppRoutingModule {}\n_AppRoutingModule = AppRoutingModule;\n_AppRoutingModule.ɵfac = function AppRoutingModule_Factory(t) {\n  return new (t || _AppRoutingModule)();\n};\n_AppRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _AppRoutingModule\n});\n_AppRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [RouterModule.forRoot(routes, {\n    preloadingStrategy: PreloadAllModules\n  }), RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["PreloadAllModules", "RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "OnboardingGuard", "PublicGuard", "routes", "path", "loadChildren", "then", "m", "WelcomePageModule", "redirectTo", "pathMatch", "OnboardingPageModule", "canActivate", "PlatformSelectionPageModule", "LoginPageModule", "GuidePageModule", "ScanBLPageModule", "DocListPageModule", "DataBLPageModule", "CropDocPageModule", "ProcessDocPageModule", "NetworkErrorPageModule", "RequestErrorPageModule", "RealtimeContoursPageModule", "DataBlSuccessPageModule", "ProfilePageModule", "MedicamentOcrPageModule", "AppRoutingModule", "forRoot", "preloadingStrategy", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Downloads\\Work __<PERSON><PERSON><PERSON><PERSON><PERSON>_ouhna\\OCR_DOCUMENT_GROSSISTE\\Frontend ocr grossiste document\\frontend_ocr_grossiste_document\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { PreloadAllModules, RouterModule, Routes } from '@angular/router';\r\nimport { AuthGuard } from './interceptors/auth.guard';\r\nimport { OnboardingGuard } from './interceptors/onboardin.guard';\r\nimport { PublicGuard } from './interceptors/public.guard';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'home',\r\n    loadChildren: () => import('./welcome/welcome.module').then( m => m.WelcomePageModule)\r\n  },\r\n  {\r\n    path: '',\r\n    redirectTo: 'onboarding',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'onboarding',\r\n    loadChildren: () => import('./onboarding/onboarding.module').then( m => m.OnboardingPageModule),\r\n    canActivate: [OnboardingGuard, PublicGuard]\r\n  },\r\n  {\r\n    path: 'welcome',\r\n    loadChildren: () => import('./welcome/welcome.module').then( m => m.WelcomePageModule),\r\n    canActivate: [PublicGuard]\r\n  },\r\n  {\r\n    path: 'platform-selection',\r\n    loadChildren: () => import('./platform-selection/platform-selection.module').then( m => m.PlatformSelectionPageModule),\r\n    canActivate: [PublicGuard]\r\n  },\r\n  {\r\n    path: 'login',\r\n    loadChildren: () => import('./login/login.module').then( m => m.LoginPageModule),\r\n    canActivate: [PublicGuard]\r\n  },\r\n  {\r\n    path: 'guide',\r\n    loadChildren: () => import('./guide/guide.module').then( m => m.GuidePageModule),\r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'scan-bl',\r\n    loadChildren: () => import('./scan-bl/scan-bl.module').then( m => m.ScanBLPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'doc-list',\r\n    loadChildren: () => import('./doc-list/doc-list.module').then( m => m.DocListPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'data-bl',\r\n    loadChildren: () => import('./data-bl/data-bl.module').then( m => m.DataBLPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'crop-doc',\r\n    loadChildren: () => import('./crop-doc/crop-doc.module').then( m => m.CropDocPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'process-doc',\r\n    loadChildren: () => import('./process-doc/process-doc.module').then( m => m.ProcessDocPageModule), \r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'network-error',\r\n    loadChildren: () => import('./network-error/network-error.module').then(m => m.NetworkErrorPageModule)\r\n  },\r\n  {\r\n    path: 'request-error',\r\n    loadChildren: () => import('./request-error/request-error.module').then( m => m.RequestErrorPageModule)\r\n  },\r\n  {\r\n    path: 'realtime-contours',\r\n    loadChildren: () => import('./realtime-contours/realtime-contours.module').then( m => m.RealtimeContoursPageModule),\r\n    canActivate: [AuthGuard]\r\n  },\r\n  {\r\n    path: 'data-bl-success',\r\n    loadChildren: () => import('./data-bl-success/data-bl-success.module').then( m => m.DataBlSuccessPageModule)\r\n  },\r\n  {\r\n    path: 'profile',\r\n    loadChildren: () => import('./profile/profile.module').then( m => m.ProfilePageModule)\r\n  },\r\n  {\r\n    path: 'medicament-ocr',\r\n    loadChildren: () => import('./medicament-ocr/medicament-ocr.module').then( m => m.MedicamentOcrPageModule)\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n];\r\n\r\n@NgModule({\r\n  imports: [\r\n    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })\r\n  ],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule { }\r\n"], "mappings": ";AACA,SAASA,iBAAiB,EAAEC,YAAY,QAAgB,iBAAiB;AACzE,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,WAAW,QAAQ,6BAA6B;;;AAEzD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACC,iBAAiB;CACtF,EACD;EACEJ,IAAI,EAAE,EAAE;EACRK,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;CACZ,EACD;EACEN,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACI,oBAAoB,CAAC;EAC/FC,WAAW,EAAE,CAACX,eAAe,EAAEC,WAAW;CAC3C,EACD;EACEE,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,CAAC;EACtFI,WAAW,EAAE,CAACV,WAAW;CAC1B,EACD;EACEE,IAAI,EAAE,oBAAoB;EAC1BC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACM,2BAA2B,CAAC;EACtHD,WAAW,EAAE,CAACV,WAAW;CAC1B,EACD;EACEE,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACO,eAAe,CAAC;EAChFF,WAAW,EAAE,CAACV,WAAW;CAC1B,EACD;EACEE,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACQ,eAAe,CAAC;EAChFH,WAAW,EAAE,CAACZ,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACS,gBAAgB,CAAC;EACrFJ,WAAW,EAAE,CAACZ,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACU,iBAAiB,CAAC;EACxFL,WAAW,EAAE,CAACZ,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACW,gBAAgB,CAAC;EACrFN,WAAW,EAAE,CAACZ,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACY,iBAAiB,CAAC;EACxFP,WAAW,EAAE,CAACZ,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,aAAa;EACnBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACa,oBAAoB,CAAC;EACjGR,WAAW,EAAE,CAACZ,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,eAAe;EACrBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACc,sBAAsB;CACtG,EACD;EACEjB,IAAI,EAAE,eAAe;EACrBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACe,sBAAsB;CACvG,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACgB,0BAA0B,CAAC;EACnHX,WAAW,EAAE,CAACZ,SAAS;CACxB,EACD;EACEI,IAAI,EAAE,iBAAiB;EACvBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACiB,uBAAuB;CAC5G,EACD;EACEpB,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACkB,iBAAiB;CACtF,EACD;EACErB,IAAI,EAAE,gBAAgB;EACtBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACmB,uBAAuB;CAC1G,CAMF;AAQD,OAAM,MAAOC,gBAAgB;oBAAhBA,gBAAgB;;mBAAhBA,iBAAgB;AAAA;;QAAhBA;AAAgB;;YAJzB5B,YAAY,CAAC6B,OAAO,CAACzB,MAAM,EAAE;IAAE0B,kBAAkB,EAAE/B;EAAiB,CAAE,CAAC,EAE/DC,YAAY;AAAA;;2EAEX4B,gBAAgB;IAAAG,OAAA,GAAAC,EAAA,CAAAhC,YAAA;IAAAiC,OAAA,GAFjBjC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}