# Git Changelog

Generated on: 2025-03-05T15:40:06.825Z

# Changelog

## v1.0.3
**Released:** 2025-03-05 15:39:58 +0000

- Force The flag isScanner in the Web also for the Gallery (0d3abfc)
- chore: release 1.0.2 by (abderrahmane.ouhna) (b9ea9a2)

## v1.0.2
**Released:** 2025-03-04 11:40:52 +0000

- Change name of the app WinPlusDoc (9e87ee5)
- chore: release 1.0.1 by (abderrahman<PERSON>.ouhna) (2157be4)

## v1.0.1
**Released:** 2025-03-03 12:45:31 +0000

- change the max-width in the web env (9253109)
- chore: release 1.0.0 by (abderrahmane.ouhna) (9b5f9d0)

## v1.0.0
**Released:** 2025-03-03 12:11:06 +0000

- feat : add nexus publisher conf (1c3c3b5)
- increase the entourage clickable for the circle in the component cropper (873d26b)
- change from WinPharmPlus to WinPlusPharma (817701e)
- Adabte the design with CamScanner App (c5639f9)
- Fix The issue of : The middle point didnt hundle the rotate case (a77b508)
- - [x] Add the middle point for crop horz and vert (51587a8)
- Hundle the issue in the case of rotate, the hundlers hide inside the div (53e0037)
- Hundle the case of the merge between the hundles (a3ea6d1)
- - [x] Create a new component about the copper drag drob SVG polygon && - [x] Add for the component magnifier (the issue of 'the .magnifier-intersection-line show now after the rotate.' still exist) (5b2c4da)
- comment the ForceGlobalSuppllier and let the user change his supplier (6d3d3c2)
- Fix bugs (87148cb)
- Fix bugs (15f844a)
- make both the polygon and rectangles in the image-cropper component clickable inside and outside the image area (af0a289)
- Trying to create the realtime_countours websocket (ccdbd6c)
- - [x] Handling vertical or large images in the image cropper (0049eb7)
- - [x] Handling vertical or large images in the image cropper (3672c96)
- OnBoarding slide modifs (bc8c939)
- Change the message Success to (vers WinpharmPlus + path url ... ) (09055ce)
- Fix issue in the remove button in the crop-doc page and other (remove all docs) (2d63d00)
- Alert confirm in the logout buttons (d99d574)
- - [x] In the Guide: Add in the first slides, another one for infor the user (Pour mieux scanner vous devez ...) - [x] In the Guide: Add the icon 'Agrandir' in the images (8d782fe)
- Login Cash Info (2391ada)
- FIx some issue + trying to fix issue of 422 (AUTRE / GLOBAL) (6c69abb)
- Fix Issue in the Guide (96ccb43)
- Fix Issue in the Guide (42d3562)
- Add another slide in the Guide component (for the image rotated) (ec04e9c)
- Notif the user for the image if maybe rotated ... (7d83876)
- Notif the user for the image if maybe rotated ... (0de3cef)
- change in the app-routing.module.ts (9863bbc)
- In the 'smart-crop' page add the button of rotate in top of image and change the old one by Retake image button (6982251)
- Check Auth Experation Token + PublicGuard (c8647e6)
- Display the list of docs in the page Home in place of 'Vous n'avez aucun document' if the data Signals is not Empty (caa6aae)
- Remove Word English 'Loading ...' (ea30573)
- Remove all icons of guids in the header of the screens (34ec26c)
- show guid in the hasSeenOnboarding=true (bc3cf83)
- add the steps login badges (36279a3)
- Fix issue mobile design (224ee0b)
- change to code grossistes (028d7fc)
- List suppliers dynamic (d34a82c)
- Tranduction Anglais to Français (9c72066)
- List suppliers dynamic (9e2a3d4)
- Tranduction Anglais to Français (5df4e6d)
- Add Deep Learnign Crop Automatisation in the camScanner Lite (67d51f2)
- Fix some issues (0b74f7a)
- change mac (486ca3b)
- Fix some issue , add new feutures ... (e665b5c)
- Fix some issue , add new feutures ... (f0c28ed)
- Hide the OnBoarding page in the first time , and show it in the dev env + filter image loading (8eebaf0)
- Hide the OnBoarding page in the first time , and show it in the dev env (89ae068)
- Add Quid button and fix some bugs (2a21821)
- ammed ... (563d26f)
- Fix issues (aedf94a)
- Fix issue for low quality and rotate with PILLOW (222475b)
- Add Profil page and Fix Some Others Bugs and issues (4747b6b)
- Add Profil page and Fix Some Others Bugs and issues (c52ea49)
- working in rotate + cropping DONE (156a6ef)
- working in rotate + cropping DONE (eed06c6)
- Add Scanner version DONE - working in rotate + cropping ... (f57634e)
- fix some issues (67e4de8)
- fix some issues (2382bdb)
- remove dark mode (55246ca)
- remove dark mode (6cb93e6)
- Fix issue of the fab-icon + the tenant login failing on the first attempt (50069bb)
- change mac (b3a8bd8)
- Intergration of Mindee API (e2581c2)
- Change amend ... (e5b639b)
- Fixing some bugs, BL Brouillant saved (eb2c86e)
- Add the BL_ID_ORIGINE and DATE_BL_ORIGINE and SUPPLIER_NAME and SUPPLIER_ID in the table SQLite (4903af9)
- Add the BL_ID_ORIGINE and DATE_BL_ORIGINE and SUPPLIER_NAME and SUPPLIER_ID in the table SQLite (f8d8ba7)
- Store data to SQLite with api WinPlus + fix some issues (5bc5e50)
- Store data to SQLite with api WinPlus (106fe14)
- management Login (94c8d33)
- Add guid for user when capturing image (07076b1)
- Add guid for user when capturing image (dd10024)
- Add guid for user when capturing image (5e81cd0)
- Add guid for user when capturing image (d218904)
- Add guid for user when capturing image (29e765c)
- Add guid for user when capturing image (c700f6d)
- Add guid for user when capturing image (7a0bf7e)
- Add guid for user when capturing image (c33d155)
- Add Block user with messages for the image (* check_size_bytes * check_resolution * check_contrast * check_lighting * clean_image * check_orientation) (570db1e)
- trying to create a service of realtime detect contouring of the image (2e3eaa7)
- add logout (53ed745)
- add logout (8bde634)
- add logout (a8525a7)
- add logout (28290df)
- backup last constants template + backup (image[y:y + h, 0:image.shape[1] TO mage[y:y + h, x:x + w) (6f7866a)
- add login autorization (3c94d33)
- add coreect skew image (8a455e0)
- Fix problem of image-cropped with 100% on the page to 90% (d688389)
- Fix error of converting to PDF in ionic (7ff4036)
- add login && send_data_to_winPlus routers (74fdffc)
- add login && send_data_to_winPlus routers (40ed9ba)
- Fixing errors (cd263aa)
- Websocket - progressbar with JobID - Done (ffef411)
- Websocket - progressbar with JobID - .... (4156102)
- Websocket - progressbar with JobID - done (b213c33)
- Websocket - progressbar with JobID - done (c002c97)
- Change Abdou PC: working for websocket progress ... (0e29130)
- Change Abdou PC: working for websocket progress ... (d699255)
- Change Abdou PC: working for websocket progress ... (a448870)
- Websocket - progressbar (6ad313a)
- Websocket - progressbar (077aa58)
- change ui (59f4a31)
- Config Jenkins + Fix error HTTP/HTTPS for the deployement mobile (6862aa1)
- Convert to PDF file in frontend and vonvert the pdf file to image in image before the processing (2224978)
- change (0c0d620)
- Convert to PDF file in frontend and vonvert the pdf file to image in image before the processing (b41ffaf)
-  change env pro to server ip adress (7b2ce8d)
- Changes pc sophatel - Cases of upload many supplier docuement in the same process (4ac7b88)
- Changes pc sophatel - Clear localStorage with remove all items (9f66f83)
- Changes pc sophatel - Zipping image in front Beofre Crop it the Backend (ae6e619)
- Changes pc sophatel - Navigation buttons (71c9a3a)
- Changes pc sophatel - Manage Exceptions (2dbd460)
- Changes pc sophatel - some changes (c25dc09)
- Changes pc sophatel - some changes (ba05040)
- Changes pc sophatel - some changes (6895f77)
- Changes pc sophatel - some changes (7856f29)
- Changes pc sophatel - some changes (2456dd6)
- Changes pc sophatel - some changes (94896ae)
- Changes pc sophatel - Fix some issus apis (91e08aa)
- Changes pc sophatel - seconde version of filter - OpenCV - SPR - SOPHACA (490961f)
- Changes pc sophatel - first version of filter magic config (4e13ad5)
- Changes pc sophatel - Data Bl ionic - Done (3042638)
- Change Abdou Pc (e098387)
- Change Abdou Pc (11c53ec)
- Changes pc sophatel - add swipper process doc (a84c8e6)
- Changes pc sophatel - add swipper process doc (47774dc)
- Changes pc sophatel - add swipper process doc (28ea417)
- Changes pc sophatel - connect the app wit server and fix some issus (5222d49)
- Change Abdou Pc (636e54c)
- Change (94d68dc)
- Changes pc sophatel filter api (65da305)
- Changes pc sophatel filter api (5653939)
- Changes pc sophatel - Add consomation api of smart-crop (7559299)
- Changes pc sophatel - Add consomation api of smart-crop (106561b)
- Fix Bugs doc-list (67c89bf)
- Fix Bugs doc-list (53f3d32)
- Change Pc Abdou (4da80ac)
- changes (6e07ff1)
- Change Pc Abdou (5ed94cb)
- Doc list forst version (00a494f)
- Doc list forst version (6d6ecec)
- Doc list forst version (c033496)
- Doc list forst version (253dc4b)
- Doc list forst version (5b6958d)
- Doc list forst version (bab23e6)
- Doc list forst version (75aaa5e)
- Doc list forst version (279797b)
- Change Pc Abdou - Fix some bugs (136eaf9)
- Change Pc Abdou - Fix some bugs (30203eb)
- Change Pc Abdou - Fix some bugs (90247ee)
- Scan BL - seconde screen (fd958c0)
- Scan BL - seconde screen (38eac69)
- Scan BL (bef5e7c)
- Welcome Page + Login Page (7fee64d)
- Welcome Page + Login Page (f826108)
- Onboarding page done 90% (339e8b8)
- OnboardPage (91e000a)

