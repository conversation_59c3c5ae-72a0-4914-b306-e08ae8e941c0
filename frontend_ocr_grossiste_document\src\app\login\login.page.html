<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="goBackToWelcome()">
        <ion-icon name="arrow-back-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Connexion</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="login-wrapper">
    <ion-row>
      <ion-col size="12" class="login-content">
        <!-- Platform indicator -->
        <div class="platform-indicator">
          <div class="platform-badge">
            <span class="platform-name">{{ getPlatformDisplayName() }}</span>
            <ion-button fill="clear" size="small" (click)="goBackToWelcome()">
              <ion-icon name="swap-horizontal-outline" slot="icon-only"></ion-icon>
            </ion-button>
          </div>
        </div>

        <!-- Step indicators for WinPlus only -->
        <div class="step-indicators" *ngIf="!isPharmalierLogin">
          <div class="step-badge" [ngClass]="{'active': showTenantLogin}">
            <span class="step-number">1</span>
            <span class="step-label">Pharmacie</span>
          </div>
          <div class="step-line"></div>
          <div class="step-badge" [ngClass]="{'active': !showTenantLogin}">
            <span class="step-number">2</span>
            <span class="step-label">Utilisateur</span>
          </div>
        </div>
        <div class="content-login-head">
          <h1 *ngIf="isPharmalierLogin">Connexion Pharmalier</h1>
          <h1 *ngIf="!isPharmalierLogin">{{ showTenantLogin ? 'Connexion de la pharmacie' : 'Connexion de l\'utilisateur' }}</h1>
          <p *ngIf="isPharmalierLogin">Utilisez vos identifiants Pharmalier</p>
          <p *ngIf="!isPharmalierLogin">Vous devez utiliser <br>les identifiants de WinPlusPharma</p>
        </div>

        <!-- WinPlus tenant login (step 1) -->
        <div class="inputs-login" *ngIf="!isPharmalierLogin && showTenantLogin">
          <ion-item lines="none" class="input-item">
            <ion-input [(ngModel)]="tenantUsername" type="text" placeholder="Identifiant de la pharmacie" required></ion-input>
          </ion-item>

          <ion-item lines="none" class="input-item">
            <ion-input [(ngModel)]="tenantPassword" type="password" placeholder="Mot de passe de la pharmacie" required></ion-input>
          </ion-item>
          <ion-button expand="block" class="login-button" (click)="tenantLogin()">Se connecter</ion-button>
        </div>

        <!-- User login (WinPlus step 2 or Pharmalien single step) -->
        <div class="inputs-login" *ngIf="isPharmalierLogin || (!isPharmalierLogin && !showTenantLogin)">
          <ion-item lines="none" class="input-item">
            <ion-input [(ngModel)]="username" type="text" [placeholder]="isPharmalierLogin ? 'Identifiant Pharmalier' : 'Identifiant de l\'utilisateur'" required></ion-input>
          </ion-item>

          <ion-item lines="none" class="input-item">
            <ion-input [(ngModel)]="password" type="password" [placeholder]="isPharmalierLogin ? 'Mot de passe Pharmalier' : 'Mot de passe de l\'utilisateur'" required></ion-input>
          </ion-item>
          <ion-button expand="block" class="login-button" (click)="userLogin()">Se connecter</ion-button>
        </div>



        <!-- Back button for WinPlus only -->
        <ion-button fill="clear" class="back-button" (click)="goBackToTenantLogin()" *ngIf="!isPharmalierLogin && !showTenantLogin">
          <ion-icon name="arrow-back-outline" slot="start"></ion-icon>
          Retour à la connexion de la pharmacie
        </ion-button>
      </ion-col>
    </ion-row>


  </div>
</ion-content>
<ion-footer>
  <img src="/assets/sophatel_logo.svg" alt="SOPHATEL Logo" class="logo">
  <p class="copyright text-center"><span> Sophatel Ingénierie </span> <br>WinDoc © 2025 - Tous droits réservés.</p> 
</ion-footer>