<ion-header>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="login-wrapper">
    <ion-row>
      <ion-col size="12" class="login-content">
        <div class="step-indicators">
          <div class="step-badge" [ngClass]="{'active': showTenantLogin}">
            <span class="step-number">1</span>
            <span class="step-label">Pharmacie</span>
          </div>
          <div class="step-line"></div>
          <div class="step-badge" [ngClass]="{'active': !showTenantLogin}">
            <span class="step-number">2</span>
            <span class="step-label">Utilisateur</span>
          </div>
        </div>
        <div class="content-login-head">
          <h1>{{ showTenantLogin ? 'Connexion de la pharmacie' : 'Connexion de l\'utilisateur' }}</h1>
          <p>Vous devez utiliser <br>les identifiants de WinPlusPharma</p>
        </div>
        
        <div class="inputs-login" *ngIf="showTenantLogin">
          <ion-item lines="none" class="input-item">
            <ion-input [(ngModel)]="tenantUsername" type="text" placeholder="Identifiant de la pharmacie" required></ion-input>
          </ion-item>
      
          <ion-item lines="none" class="input-item">
            <ion-input [(ngModel)]="tenantPassword" type="password" placeholder="Mot de passe de la pharmacie" required></ion-input>
          </ion-item>
          <ion-button expand="block" class="login-button" (click)="tenantLogin()" *ngIf="showTenantLogin">Se connecter</ion-button>
        </div>

        <div class="inputs-login" *ngIf="!showTenantLogin">
          <ion-item lines="none" class="input-item">
            <ion-input [(ngModel)]="username" type="text" placeholder="Identifiant de l'utilisateur" required></ion-input>
          </ion-item>
      
          <ion-item lines="none" class="input-item">
            <ion-input [(ngModel)]="password" type="password" placeholder="Mot de passe de l'utilisateur" required></ion-input>
          </ion-item>
          <ion-button expand="block" class="login-button" (click)="userLogin()" *ngIf="!showTenantLogin">Se connecter</ion-button>
        </div>


        
        <ion-button fill="clear" class="back-button" (click)="goBackToTenantLogin()" *ngIf="!showTenantLogin">
          <ion-icon name="arrow-back-outline" slot="start"></ion-icon>
          Retour à la connexion de la pharmacie
        </ion-button>
      </ion-col>
    </ion-row>


  </div>
</ion-content>
<ion-footer>
  <img src="/assets/sophatel_logo.svg" alt="SOPHATEL Logo" class="logo">
  <p class="copyright text-center"><span> Sophatel Ingénierie </span> <br>WinDoc © 2025 - Tous droits réservés.</p> 
</ion-footer>