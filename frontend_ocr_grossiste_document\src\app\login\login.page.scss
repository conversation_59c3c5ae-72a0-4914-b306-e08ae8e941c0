@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
}

ion-content::part(scroll) {
  // overflow-y: hidden !important;
  // --overflow: hidden !important;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.login-wrapper {
  background: url("/assets/bg-welcome.png") no-repeat center center fixed;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: space-between; // Changed from space-around
  position: relative;
  padding-bottom: 80px; // Add padding for footer
}

.content-login-head {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-bottom: 30px;
}

.content-login-head h1 {
  color: #1f41bb;
  font-size: 34px;
  font-weight: bold;
  text-shadow: 0px 0px 1.5px #1f41bb;
  text-align: center;
}
.content-login-head p {
  color: #141414;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  text-shadow: 0px 0px 0.5px #050505;
}

ion-toolbar {
  --background: transparent;
  --ion-color-primary: #2f4fcd;
}

ion-button.login-button {
  --background: #1f41bb;
  --background-activated: #1f41bb;
  --border-radius: 10px;
  // width: 80%;
  text-align: center;
  box-shadow: 0px 16px 20px rgba(203, 214, 255, 1); /* Add shadow */
  color: #fff;
  font-size: 20px;
  font-weight: bold;
}

ion-button::part(native) {
  --padding-top: 20px !important;
  --padding-bottom: 20px !important;
}
ion-row {
  // height: 100%;
  // height: 100vh;
}

::ng-deep ion-col {
  display: flex !important;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.inputs-login {
  display: flex;
  gap: 30px;
  flex-direction: column;
  width: 85%;
  // margin-bottom: 20px;
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //     flex-direction: column;
  //     margin: 0 40px;
  animation: fadeIn 0.3s ease-in-out;
}

::ng-deep .login-content {
  margin-bottom: 5rem;
}

::ng-deep .login-content ion-item {
  width: 100%;
  padding-right: 50px;
  font-size: 16px;
  font-weight: 600;
  color: #050505;
  text-align: left;
  // margin: 15px 0;
  padding: 8px 10px;
  border: 2px solid #1f41bb;
  border-radius: 10px;
  --background: #f1f4ff; /* Background color for input fields */
  background-color: #f1f4ff;
}

// .login-content ion-item, .login-content ion-item ion-input

.forgot-password {
  width: 100%;
  padding-right: 50px;
  font-size: 16px;
  font-weight: 600;
  color: #1f41bb;
  text-align: right;
  text-decoration: none;
  margin-bottom: 20px;
}

ion-footer {
  position: fixed; // Change to fixed
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.9); // Optional: add background
  padding: 10px;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;

  // Optional: add transition for smooth movement
  transition: transform 0.3s ease-out;

  &.keyboard-open {
    transform: translateY(100%); // Hide footer when keyboard is open
  }
}

.copyright {
  color: #1f41bb;
  padding-top: 5px;
  font-weight: 500;
  font-size: 14px;
  text-shadow: 0px 0px 1px #1f41bb;
  text-align: center;
  span {
    font-size: 16px;
    font-weight: bold;
  }
}

// Platform indicator
.platform-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  margin-top: 10px;
}

.platform-badge {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 8px 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .platform-name {
    color: white;
    font-weight: 600;
    margin-right: 8px;
    text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.5);
  }

  ion-button {
    --color: white;
    margin: 0;
    --padding-start: 8px;
    --padding-end: 8px;
  }
}

// Steps login
.step-indicators {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  margin-top: 10px;
  width: 100%;
  max-width: 300px;
  // position: sticky;
  // top: 0;
}

.step-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;

  .step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f1f4ff;
    border: 2px solid #1f41bb;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1f41bb;
    font-weight: bold;
    font-size: 18px;
    transition: all 0.3s ease;
  }

  .step-label {
    margin-top: 8px;
    color: #1f41bb;
    font-size: 14px;
    font-weight: 500;
  }

  &:hover {
    cursor: pointer;
    transform: translateY(-2px);
  }

  &.active {
    .step-number {
      background-color: #1f41bb;
      color: white;
      transform: scale(1.1);
    }
    .step-label {
      font-weight: 600;
    }
  }
}

.step-line {
  flex: 1;
  height: 2px;
  background-color: #1f41bb;
  margin: 0 15px;
  margin-bottom: 20px;
  max-width: 100px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
